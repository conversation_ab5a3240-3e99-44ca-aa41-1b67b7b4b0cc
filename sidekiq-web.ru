#!/usr/bin/env ruby
# REDIS_URL="redis://localhost:6379/3/imperator" bundle exec rackup sidekiq-web.ru --port 9294 --host localhost -P `pwd`/tmp/pids/sidekiq-web.pid -D -E development
require 'sidekiq'
require 'sidekiq/web'
require 'sidekiq-scheduler/web'
require 'sidekiq-statistic'
require 'sidekiq-status'
require 'sidekiq/failures'

Sidekiq.configure_client do |config|
  config.redis = {
    url: ENV['REDIS_URL'],
    size: 1
  }

  Sidekiq::Status.configure_client_middleware config
end

map '/' do
  run Sidekiq::Web
end
