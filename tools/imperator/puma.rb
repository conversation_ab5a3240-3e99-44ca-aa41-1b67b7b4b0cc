#!/usr/bin/env puma

directory '/app'
rackup '/app/config.ru'
environment 'production'

tag ''

threads 4, 16
workers 0
restart_command 'bundle exec puma'
preload_app!

on_restart do
  puts 'Refreshing Gemfile'
  ENV['BUNDLE_GEMFILE'] = ''
end

before_fork do
  ActiveRecord::Base.connection_pool.disconnect!
end

on_worker_boot do
  ActiveSupport.on_load(:active_record) do
    ActiveRecord::Base.establish_connection
  end
end
