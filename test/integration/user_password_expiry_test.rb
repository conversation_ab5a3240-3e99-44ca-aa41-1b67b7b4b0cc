require 'test_helper'
include Warden::Test::Helpers

class UserPasswordExpiryTest < ActiveSupport::TestCase
  setup do
    @old_period = Devise.expire_password_after
    Devise.expire_password_after = 2.month
  end

  teardown do
    Devise.expire_password_after = @old_period
  end

  test 'password auto-expires for newly created user' do
    user = User.new(first_name: '<PERSON>',
                    last_name: '<PERSON>',
                    activates_on: Time.zone.today,
                    company_id: companies(:mkalita_company).id)
    user.username, user.email = UsernameAndEmailGenerator.new.call(user)
    user.save
    assert user.persisted?, user.errors.inspect
    assert user.need_change_password?
    password = generate_valid_password
    user.password = password
    user.password_confirmation = password
    user.save(validate: false)
    refute user.need_change_password?
  end

  test 'override expire after at runtime' do
    user = User.new(first_name: '<PERSON>',
                    last_name: '<PERSON>',
                    username: 'new_api_user',
                    email: '<EMAIL>',
                    password: '123qweR!',
                    password_confirmation: '123qweR!',
                    activates_on: Time.zone.today)
    user.username, user.email = UsernameAndEmailGenerator.new.call(user)
    user.save
    user.instance_eval do
      def expire_password_after
        4.month
      end
    end
    user.password_changed_at = Time.now.ago(3.month)
    refute user.need_change_password?
    user.password_changed_at = Time.now.ago(5.month)
    assert user.need_change_password?
  end
end
