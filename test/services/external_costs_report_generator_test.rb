require 'test_helper'

class ExternalCostsReportGeneratorTest < ActiveSupport::TestCase
  test 'generates xlsx file without errors' do
    external_costs = ExternalCost.all

    assert_nothing_raised do
      ExternalCostsReportGenerator.generate(external_costs)
    end
  end

  test 'generates xlsx with correct data for an external cost' do
    external_cost = external_costs(:project_one_next_month)

    external_cost.update_column(:comment, 'Test comment') unless external_cost.comment.present?

    xlsx = ExternalCostsReportGenerator.generate(ExternalCost.where(id: external_cost.id))

    assert xlsx.is_a?(StringIO)
    assert xlsx.length > 0
  end
end
