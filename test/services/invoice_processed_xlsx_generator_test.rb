require 'test_helper'

class InvoiceProcessedXlsxGeneratorTest < ActiveSupport::TestCase
  test 'it generate without errors' do
    invoices(:project_five_second_payment_invoice_amendment).accepted!
    issued = Invoice.issued
    accepted = Invoice.accepted

    assert_nothing_raised do
      InvoicesProcessedXlsxGenerator.new(issued, accepted).generate
    end
  end

  test 'it include correct values for regular invoices' do
    accepted_invoice = invoices(:project_five_second_payment_invoice_amendment)
    accepted_invoice.accepted!

    issued = Invoice.issued
    accepted = Invoice.accepted

    issued_invoice = issued.first

    xlsx = InvoicesProcessedXlsxGenerator.new(issued, accepted).generate

    Zip::File.open_buffer(xlsx) do |zipfile|
      zipfile.each do |entry|
        if entry.name == 'xl/worksheets/sheet1.xml'
          sheet_xml = entry.get_input_stream.read

          assert_includes sheet_xml, issued_invoice.id.to_s
          assert_includes sheet_xml, 'Issued at'
          assert_includes sheet_xml, 'Currency'
          assert_includes sheet_xml, 'Paper invoice'
          assert_includes sheet_xml, 'E-mail invoice'
          assert_includes sheet_xml, 'Invoice document'
          assert_not_includes sheet_xml, 'Accepted at'
        end
        if entry.name == 'xl/worksheets/sheet2.xml'
          sheet_xml = entry.get_input_stream.read

          assert_includes sheet_xml, accepted_invoice.id.to_s
          assert_includes sheet_xml, 'Accepted at'
          assert_includes sheet_xml, 'Currency'
          assert_includes sheet_xml, 'Invoice document'
          assert_includes sheet_xml, 'Paper invoice'
          assert_includes sheet_xml, 'E-mail invoice'
          assert_not_includes sheet_xml, 'Issued at'
        end
      end
    end
  end
end
