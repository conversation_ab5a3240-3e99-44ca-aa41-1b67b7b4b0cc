require 'test_helper'

class ClickUpTimeEntriesImporterTest < ActiveSupport::TestCase
  let(:importer) { ClickUpTimeEntriesImporter.new }
  let(:task_id) { 'test_task_123' }
  let(:time_entries) { [
    {
      'id' => 'time_entry_1',
      'task' => { 'id' => task_id, 'name' => 'Test Task' },
      'user' => { 'id' => 'user_2', 'username' => 'testuser' },
      'intervals' => [
        {
          'id' => 5548645132,
          'start' => 1609459200000,
          'time' => 3600000,
          'end' => 1609462800000
        },
        {
          'id' => 5548565151315,
          'start' => 1609462800000,
          'time' => 1800000,
          'end' => 1609464600000
        }
      ]
    }
  ] }
  let(:api_response) { { 'data' => time_entries } }

  test 'import fetches time entries for a task' do
    ClickUpApi.expects(:get)
              .with("/task/#{task_id}/time", query: { limit: ClickUpImporter::DEFAULT_STEP } )
              .returns(api_response)

    result = importer.import(task_id)

    assert_equal time_entries, result
    assert_equal 2, result.first['intervals'].size
  end

  test 'import with custom limit' do
    custom_limit = 50
    importer.limit(custom_limit)

    ClickUpApi.expects(:get)
              .with("/task/#{task_id}/time", query: { limit: custom_limit })
              .returns(api_response)

    result = importer.import(task_id)

    assert_equal time_entries, result
  end

  test 'import handles empty response' do
    empty_response = { 'data' => [] }

    ClickUpApi.expects(:get)
              .with("/task/#{task_id}/time", query: { limit: ClickUpImporter::DEFAULT_STEP })
              .returns(empty_response)

    result = importer.import(task_id)

    assert_empty result
  end
end
