require 'test_helper'

class InvoicesMonthlyReportGeneratorTest < ActiveSupport::TestCase
  setup do
    Payment.includes(:mpk_positions, :payment_schedule).find_each do |payment|
      payment.send :recalculate_scheduled_payments
    end
    Invoice.where(state: %i[pending accepted issued])
           .includes(:mpk_positions, payment: :payment_schedule).find_each do |invoice|
      invoice.send :recalculate_payments
    end
  end

  test 'generates csv representing invoices issued on specific month' do
    params = {
      date_from: Time.zone.now.beginning_of_month.to_fs('YYYY-MM-DD'),
      date_to: Time.zone.now.end_of_month.to_fs('YYYY-MM-DD'),
      attribute_name: 'invoice_date'
    }

    report = InvoicesMonthlyReportGenerator.generate(params)

    assert report.match(clients(:arte).name)
  end

  test 'is able to filter invoices by accounting number' do
    params = {
      date_from: Time.zone.now.beginning_of_month.to_fs('YYYY-MM-DD'),
      date_to: Time.zone.now.end_of_month.to_fs('YYYY-MM-DD'),
      attribute_name: 'invoice_date'
    }

    accounting_number_with_invoices = accounting_numbers(:one)
    accounting_number_without_invoices = accounting_numbers(:locked)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({accounting_number_id: accounting_number_with_invoices.id}))

    assert report.match(clients(:arte).name)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({accounting_number_id: accounting_number_without_invoices.id}))

    assert_not report.match(clients(:arte).name)
  end

  test 'filters invoices by due date' do
    params = {
      date_from: 6.days.from_now.to_fs('YYYY-MM-DD'),
      date_to: 8.days.from_now.to_fs('YYYY-MM-DD'),
      attribute_name: 'due_date'
    }

    accounting_number_with_invoices = accounting_numbers(:one)
    accounting_number_without_invoices = accounting_numbers(:locked)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({accounting_number_id: accounting_number_with_invoices.id}))

    assert report.match(clients(:arte).name)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({accounting_number_id: accounting_number_without_invoices.id}))

    assert_not report.match(clients(:arte).name)
  end

  test 'filters invoices by sell date' do
    params = {
      date_from: Time.zone.now.beginning_of_month.to_fs('YYYY-MM-DD'),
      date_to: Time.zone.now.end_of_month.to_fs('YYYY-MM-DD'),
      attribute_name: 'sell_date'
    }

    accounting_number_with_invoices = accounting_numbers(:one)
    accounting_number_without_invoices = accounting_numbers(:locked)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({accounting_number_id: accounting_number_with_invoices.id}))

    assert report.match(clients(:arte).name)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({accounting_number_id: accounting_number_without_invoices.id}))

    assert_not report.match(clients(:arte).name)
  end

  test 'generates csv representing invoices issued on specific month with company filter' do
    efigence = companies(:two)
    kontomierz = companies(:three)

    params = {
      date_from: Time.zone.now.beginning_of_month.to_fs('YYYY-MM-DD'),
      date_to: Time.zone.now.end_of_month.to_fs('YYYY-MM-DD'),
      attribute_name: 'invoice_date',
    }

    report = InvoicesMonthlyReportGenerator.generate(params.merge({company_id: efigence.id}))

    assert report.match(efigence.name)
    assert_not report.match(kontomierz.name)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({company_id: kontomierz.id}))

    assert report.match(kontomierz.name)
    assert_not report.match(efigence.name)
  end

  test 'generates csv representing invoices issued on specific month with project filter' do
    project_two = projects(:two)
    project_five = projects(:five)

    params = {
      date_from: Time.zone.now.beginning_of_month.to_fs('YYYY-MM-DD'),
      date_to: Time.zone.now.end_of_month.to_fs('YYYY-MM-DD'),
      attribute_name: 'invoice_date',
    }

    report = InvoicesMonthlyReportGenerator.generate(params.merge({project_id: project_two.id}))

    assert report.match(project_two.name)
    assert_not report.match(project_five.name)

    report = InvoicesMonthlyReportGenerator.generate(params.merge({project_id: project_five.id}))

    assert report.match(project_five.name)
    assert_not report.match(project_two.name)
  end
end
