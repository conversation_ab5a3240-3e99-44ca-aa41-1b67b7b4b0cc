require 'test_helper'

class AccountingNumbersDictionaryGeneratorTest < ActiveSupport::TestCase
  test 'generates dictionary properly' do
    company = companies(:one)
    accounting_number1 = accounting_numbers(:one)
    accounting_number2 = accounting_numbers(:two)

    xml = AccountingNumbersDictionaryGenerator.generate(company)

    hash = Hash.from_xml(xml)

    assert(hash['ROOT']['PROJEKT'].compact.detect do |node|
      node['ID'] == accounting_number1.number.to_s
    end)
    assert_not(hash['ROOT']['PROJEKT'].compact.detect do |node|
      node['ID'] == accounting_number2.number.to_s
    end)
  end
end
