require 'test_helper'

class UsernameAndEmailGeneratorTest < ActiveSupport::TestCase
  test 'generates username and email with aliases for efi/arte' do
    u = User.new(first_name: '<PERSON>', last_name: 'Locked', activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    generated_username_emails = UsernameAndEmailGenerator.new.call(u)
    target_emails = Set.new(
      %w(<EMAIL> <EMAIL>
         <EMAIL> <EMAIL>
         <EMAIL>)
    )
    emails = Set.new(generated_username_emails.pop)
    assert_equal target_emails, emails
    assert_equal ['jlocked', '<EMAIL>'], generated_username_emails
  end

  test 'generates username and email with aliases for filmweb' do
    u = User.new(first_name: '<PERSON>', last_name: 'Locked', activates_on: Time.zone.today,
                 company: companies(:filmweb))
    generated_username_emails = UsernameAndEmailGenerator.new.call(u)
    target_emails = Set.new(%w(<EMAIL>
                               <EMAIL>))
    emails = Set.new(generated_username_emails.pop)
    assert_equal target_emails, emails
    assert_equal ['jlocked', '<EMAIL>'], generated_username_emails
  end

  test 'generates unique username and email from surname with plus 1 for email aliases' do
    u = User.new(first_name: 'John',
                 last_name: 'Locked+1',
                 username: 'jlocked',
                 email: '<EMAIL>',
                 password: 'asdASD123',
                 password_confirmation: 'asdASD123',
                 activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    generated = UsernameAndEmailGenerator.new.call(u)
    assert_equal ['jlocked1', '<EMAIL>'], generated
  end

  test 'generates username and email properly for two-part name' do
    user = users(:mkalita_user)
    user.last_name = 'Bachleda-Curuś'
    g = UsernameAndEmailGenerator.new
    username, email = g.call(user)
    assert_equal 'mbachleda', username
    assert_equal '<EMAIL>', email
  end

  test 'generates unique username and email' do
    u = User.new(first_name: 'John',
                 last_name: 'Locked',
                 username: 'jlocked',
                 email: '<EMAIL>',
                 password: 'asdASD123',
                 password_confirmation: 'asdASD123',
                 activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    u.save
    u2 = User.new(first_name: 'John',
                  last_name: 'Locked',
                  username: 'jlocked',
                  email: '<EMAIL>',
                  password: 'asdASD123',
                  password_confirmation: 'asdASD123',
                  activates_on: Time.zone.today,
                  company_id: companies(:two).id)
   generated = UsernameAndEmailGenerator.new.call(u2).first(2)
    assert_equal ['jlocked1', '<EMAIL>'], generated
  end

  test 'generator unique number assignment' do
    u = users(:milosz)
    u2 = User.new(first_name: 'Miłosz', last_name: 'Grabowski', activates_on: Time.zone.today,
                  company: u.company)
    username = UsernameAndEmailGenerator.new.call(u2).first
    assert_equal 'mgrabowski1', username
  end

  test 'generator ignores numbers which are not at the end' do
    u = User.new(first_name: 'John',
                 last_name: 'Locke99d',
                 username: 'jlocke99d',
                 email: '<EMAIL>',
                 password: 'asdASD123',
                 password_confirmation: 'asdASD123',
                 activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    u.save
    u2 = User.new(first_name: 'John',
                  last_name: 'Locke99d',
                  password: 'asdASD123',
                  password_confirmation: 'asdASD123',
                  activates_on: Time.zone.today,
                  company_id: companies(:two).id)
    generated = UsernameAndEmailGenerator.new.call(u2).first(2)
    assert_equal ['jlocke99d1', '<EMAIL>'], generated
  end

  test 'generate username and email for users without company' do
    u = User.new(first_name: 'Mark',
                 last_name: 'Smith',
                 username: 'marksmith',
                 email: '<EMAIL>',
                 password: 'asdASD123',
                 password_confirmation: 'asdASD123',
                 activates_on: Time.zone.today,
                 company_id: nil)
    assert_equal ['<EMAIL>', '<EMAIL>'], UsernameAndEmailGenerator.new.call(u)
  end

  test 'genarator loops until it finds a valid username' do
    u = User.new(first_name: 'John',
                 last_name: 'Smith',
                 username: 'jkowalski',
                 company_id: *********,
                 email: '<EMAIL>',
                 password: 'asdASD123',
                 activates_on: Time.zone.today,
                 password_confirmation: 'asdASD123')
    u.username, u.email = UsernameAndEmailGenerator.new.call(u)
    u.save
    assert User.where(username: 'jsmith').exists?
    u2 = User.new(first_name: 'John',
                  last_name: 'Smith',
                  username: 'jkowalski1',
                  email: '<EMAIL>',
                  company_id: *********,
                  password: 'asdASD123',
                  password_confirmation: 'asdASD123',
                  activates_on: Time.zone.today)
    u2.username, u2.email = UsernameAndEmailGenerator.new.call(u2)
    u2.save
    assert User.where(username: 'jsmith1').exists?
    u3 = User.new(first_name: 'John',
                  last_name: 'Smith',
                  username: 'nibyjankowalski',
                  email: '<EMAIL>',
                  company_id: *********,
                  password: 'asdASD123',
                  password_confirmation: 'asdASD123',
                  activates_on: Time.zone.today)
    u3.username, u3.email = UsernameAndEmailGenerator.new.call(u3)
    u3.save
    assert_equal 'jsmith2', u3.username
    assert_equal '<EMAIL>', u3.email
    u4 = User.new(first_name: 'John',
                  last_name: 'Smith',
                  username: 'mozejankowalski',
                  email: '<EMAIL>',
                  company_id: *********,
                  password: 'asdASD123',
                  password_confirmation: 'asdASD123',
                  activates_on: Time.zone.today)
    u4.username, u4.email = UsernameAndEmailGenerator.new.call(u4)
    u4.save
    assert_equal 'jsmith3', u4.username
    assert_equal '<EMAIL>', u4.email
  end

  test 'dont change already valid username and email in an existing user' do
    u = User.new(first_name: 'John',
                 last_name: 'Locked',
                 username: 'jlocked',
                 email: '<EMAIL>',
                 password: 'asdASD123',
                 password_confirmation: 'asdASD123',
                 activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    generated = UsernameAndEmailGenerator.new.call(u).first(2)
    assert_equal ['jlocked', '<EMAIL>'], generated
  end

  test 'changes persisted users username and email if needed' do
    u = User.new(first_name: 'John',
                 last_name: 'Locked',
                 username: 'jlocked',
                 email: '<EMAIL>',
                 password: 'asdASD123',
                 password_confirmation: 'asdASD123',
                 activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    u.save
    u2 = User.new(first_name: 'John',
                  last_name: 'Locked',
                  username: 'asdasdasdasd',
                  email: '<EMAIL>',
                  password: 'asdASD123',
                  password_confirmation: 'asdASD123',
                  activates_on: Time.zone.today,
                  company_id: companies(:two).id)
    generated = UsernameAndEmailGenerator.new.call(u2).first(2)
    assert_equal ['jlocked1', '<EMAIL>'], generated
  end
end
