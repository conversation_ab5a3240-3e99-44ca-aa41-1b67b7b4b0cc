require 'test_helper'

class UnpaidInvoicesXlsxReaderTest < ActiveSupport::TestCase
  setup do
    @client = clients(:arte)
    @client.update!(vat_number: '1234567890', invoice_sending_email: '<EMAIL>')

    # Mock the mailer to avoid rendering templates
    mock_mail = mock('mail')
    mock_mail.stubs(:html_part).returns(mock('html_part', body: mock('body', raw_source: '<p>Test email content</p>')))
    InvoicesMailer.stubs(:unpaid_invoices_to_five).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_six_to_thirty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_thirty_one_to_sixty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_sixty_one_to_one_twenty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_over_one_twenty).returns(mock_mail)
  end

  test 'reads valid XLSX file with unpaid invoices' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 1000.0,
        end_sum: 1000.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length

    invoice_data = result.first
    assert_equal 'Artegence', invoice_data[:client]
    assert_equal 'unpaid_invoices_to_five', invoice_data[:overdue_status]
    assert_equal 'Test Company', invoice_data[:company]
    assert_equal 'PLN', invoice_data[:currency]
    assert_equal 1000.0, invoice_data[:amount]
    assert_equal ['INV-001'], invoice_data[:invoices_numbers]
    assert_equal '<EMAIL>', invoice_data[:emails]
    assert invoice_data[:checked]
    assert_not_nil invoice_data[:content]
  end

  test 'groups multiple invoices by client and overdue status' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        to_five: 300.0,
        end_sum: 300.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length

    invoice_data = result.first
    assert_equal 'Artegence', invoice_data[:client]
    assert_equal 800.0, invoice_data[:amount]
    assert_equal ['INV-001', 'INV-002'], invoice_data[:invoices_numbers]
  end

  test 'creates separate groups for different overdue statuses' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        six_to_thirty: 300.0,
        end_sum: 300.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 2, result.length

    to_five_group = result.find { |r| r[:overdue_status] == 'unpaid_invoices_to_five' }
    six_to_thirty_group = result.find { |r| r[:overdue_status] == 'unpaid_invoices_six_to_thirty' }

    assert_not_nil to_five_group
    assert_not_nil six_to_thirty_group

    assert_equal 500.0, to_five_group[:amount]
    assert_equal ['INV-001'], to_five_group[:invoices_numbers]

    assert_equal 300.0, six_to_thirty_group[:amount]
    assert_equal ['INV-002'], six_to_thirty_group[:invoices_numbers]
  end

  test 'skips paid invoices' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: 'PAID',
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        to_five: 300.0,
        end_sum: 300.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length
    assert_equal ['INV-002'], result.first[:invoices_numbers]
  end

  test 'skips invoices with blank VAT number' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_empty result
  end

  test 'skips invoices with blank invoice number' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: '',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_empty result
  end

  test 'handles client not found by VAT number' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: 'NONEXISTENT',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_empty result
  end

  test 'handles client with blank email' do
    @client.update!(invoice_sending_email: '')

    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length
    assert_equal '', result.first[:emails]
    assert_not result.first[:checked]
  end

  test 'raises error when headers not found' do
    file = create_empty_xlsx_file

    assert_raises(RuntimeError, 'Headers not found') do
      UnpaidInvoicesXlsxReader.new(file)
    end
  end

  test 'sorts results by client name and overdue status' do
    client_b = clients(:polexit)
    client_b.update!(vat_number: '9876543210', invoice_sending_email: '<EMAIL>')

    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '9876543210',
        invoice_number: 'INV-003',
        currency: 'PLN',
        is_paid: nil,
        six_to_thirty: 200.0,
        end_sum: 200.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '9876543210',
        invoice_number: 'INV-004',
        currency: 'PLN',
        is_paid: nil,
        to_five: 100.0,
        end_sum: 100.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 3, result.length

    # Should be sorted by client name first, then by overdue status order
    assert_equal 'Artegence', result[0][:client]
    assert_equal 'unpaid_invoices_to_five', result[0][:overdue_status]

    assert_equal 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego', result[1][:client]
    assert_equal 'unpaid_invoices_to_five', result[1][:overdue_status]

    assert_equal 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego', result[2][:client]
    assert_equal 'unpaid_invoices_six_to_thirty', result[2][:overdue_status]
  end

  test 'handles all overdue status types' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 100.0,
        end_sum: 100.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        six_to_thirty: 200.0,
        end_sum: 200.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-003',
        currency: 'PLN',
        is_paid: nil,
        thirty_one_to_sixty: 300.0,
        end_sum: 300.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-004',
        currency: 'PLN',
        is_paid: nil,
        sixty_one_to_one_twenty: 400.0,
        end_sum: 400.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-005',
        currency: 'PLN',
        is_paid: nil,
        over_one_twenty: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 5, result.length

    statuses = result.map { |r| r[:overdue_status] }
    expected_statuses = [
      'unpaid_invoices_to_five',
      'unpaid_invoices_six_to_thirty',
      'unpaid_invoices_thirty_one_to_sixty',
      'unpaid_invoices_sixty_one_to_one_twenty',
      'unpaid_invoices_over_one_twenty'
    ]

    assert_equal expected_statuses, statuses
  end

  test 'handles multiple clients with multiple emails' do
    @client.update!(invoice_sending_email: '<EMAIL>, <EMAIL>')

    client_b = clients(:polexit)
    client_b.update!(vat_number: '9876543210', invoice_sending_email: '<EMAIL>')

    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '9876543210',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        to_five: 300.0,
        end_sum: 300.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 2, result.length

    arte_result = result.find { |r| r[:client] == 'Artegence' }
    polexit_result = result.find { |r| r[:client] == 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego' }

    assert_equal '<EMAIL>, <EMAIL>', arte_result[:emails]
    assert_equal '<EMAIL>', polexit_result[:emails]
  end

  private

  def create_test_xlsx_file(data)
    require 'caxlsx'

    package = Axlsx::Package.new
    workbook = package.workbook

    workbook.add_worksheet(name: "Test") do |sheet|
      # Add some empty rows to simulate real file structure
      6.times { sheet.add_row([]) }

      # Add header row
      sheet.add_row([
        'Firma', '', 'Podmiot Kod', 'Dokument Numer', '', '', 'Waluta', 'Terminowe', '',
        '1. nie więcej niż 5 dni', '', '2. 6 - 30 dni', '', '3. 31 - 60 dni', '',
        '4. 61 - 120 dni', '', '5. powyżej 120 dni', '', 'Razem'
      ])

      # Add data rows
      data.each do |row_data|
        sheet.add_row([
          row_data[:company], '', row_data[:vat], row_data[:invoice_number], '', '',
          row_data[:currency], row_data[:is_paid], '', row_data[:to_five], '',
          row_data[:six_to_thirty], '', row_data[:thirty_one_to_sixty], '',
          row_data[:sixty_one_to_one_twenty], '', row_data[:over_one_twenty], '',
          row_data[:end_sum]
        ])
      end
    end

    # Create a temporary file
    temp_file = Tempfile.new(['test_invoices', '.xlsx'])
    temp_file.binmode
    temp_file.write(package.to_stream.read)
    temp_file.rewind

    # Create an uploaded file object
    uploaded_file = Rack::Test::UploadedFile.new(temp_file.path, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    uploaded_file.original_filename = 'test_invoices.xlsx'
    uploaded_file
  end

  def create_empty_xlsx_file
    require 'caxlsx'

    package = Axlsx::Package.new
    workbook = package.workbook
    workbook.add_worksheet(name: "Empty") do |sheet|
      # Just empty rows
      5.times { sheet.add_row([]) }
    end

    temp_file = Tempfile.new(['empty', '.xlsx'])
    temp_file.binmode
    temp_file.write(package.to_stream.read)
    temp_file.rewind

    uploaded_file = Rack::Test::UploadedFile.new(temp_file.path, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    uploaded_file.original_filename = 'empty.xlsx'
    uploaded_file
  end
end
