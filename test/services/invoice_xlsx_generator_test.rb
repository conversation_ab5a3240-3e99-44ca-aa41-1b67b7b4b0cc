require 'test_helper'

class InvoiceXlsxGeneratorTest < ActiveSupport::TestCase
  test 'it passes for regular invoice' do
    invoice = invoices(:payment_four)

    assert_nothing_raised do
      InvoiceXlsxGenerator.new(invoice).generate
    end
  end

  test 'it passes for amendment' do
    invoice = invoices(:payment_four_amendment)

    assert_nothing_raised do
      InvoiceXlsxGenerator.new(invoice).generate
    end
  end
end
