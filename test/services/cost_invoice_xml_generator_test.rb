require 'test_helper'

class CostInvoiceXmlGeneratorTest < ActiveSupport::TestCase
  test 'generates xml file for accepted cost invoice' do
    cost_invoice = cost_invoices(:wiktoria_cost_invoice)

    xml = CostInvoiceXmlGenerator.generate(cost_invoice)

    assert xml.match(cost_invoice.contractor.name)
  end

  test 'generates xml file for more than one cost invoice' do
    cost_invoice1 = cost_invoices(:wiktoria_cost_invoice)
    cost_invoice2 = cost_invoices(:mikolaj_cost_invoice)
    cost_invoice3 = cost_invoices(:dms_accepted_cost_invoice)
    company = companies(:one)

    xml = CostInvoiceXmlGenerator.generate([cost_invoice1, cost_invoice2, cost_invoice3], company)

    assert xml.match(cost_invoice1.contractor.name)
    assert xml.match(cost_invoice2.contractor.name)
    assert xml.match(cost_invoice3.contractor.name)
  end

  test 'generates xml file for dms cost invoice' do
    cost_invoice = cost_invoices(:dms_cost_invoice_project)
    company = companies(:one)

    xml = CostInvoiceXmlGenerator.generate(cost_invoice, company)

    assert xml.match(cost_invoice.contractor.name)
  end

  test 'generates xml file for dms proforma cost invoice' do
    cost_invoice = cost_invoices(:dms_cost_invoice_project)
    cost_invoice.update(kind: :proforma)
    company = companies(:one)

    xml = CostInvoiceXmlGenerator.generate(cost_invoice, company)

    assert xml.match('PROFORMA')
  end

  test 'generates xml file for dms correction cost invoice' do
    number = 'A/2351/2'
    cost_invoice = cost_invoices(:dms_cost_invoice_project)
    cost_invoice.update(kind: :correction, original_document_number: number)
    company = companies(:one)

    xml = CostInvoiceXmlGenerator.generate(cost_invoice, company)

    assert xml.match('<KOREKTA>Tak</KOREKTA>')
    assert xml.match(number)
  end

  test 'generates xml file for negative dms correction cost invoice' do
    number = 'A/2351/2'
    cost_invoice = cost_invoices(:dms_cost_invoice_project)
    position = cost_invoice.cost_invoice_positions.first
    position.update(unit_price: -position.unit_price, net_value: -position.net_value)
    cost_projects = cost_invoice.cost_projects
    cost_projects.each { |cost_project| cost_project.update(amount: -cost_project.amount) }
    cost_invoice.update(kind: :correction, original_document_number: number)
    company = companies(:one)

    xml = CostInvoiceXmlGenerator.generate(cost_invoice, company)

    assert xml.match('<KIERUNEK>przychód</KIERUNEK>')
    assert xml.match(number)
  end

  test 'escapes special characters in invoice' do
    number = "31BBB801\u00000012"
    cost_invoice = cost_invoices(:dms_cost_invoice_project)
    cost_invoice.number = number
    cost_invoice.save(validate: false)
    company = companies(:one)

    xml = CostInvoiceXmlGenerator.generate(cost_invoice, company)

    assert xml.match('31BBB8010012')
  end
end
