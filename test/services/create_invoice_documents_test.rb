require 'test_helper'

class CreateInvoiceDocumentsTest < ActiveSupport::TestCase
  setup do
    @company = companies(:two)

    FileUtils.touch(
      Dir[File.join(Settings.invoices_integration.estelligence_share_path, @company.name, 'KSIĘGOWOŚĆ/sprzedaż/**/*.{pdf,PDF}')]
    )
  end

  test 'creates invoice documents' do
    invoice1, invoice2 = Invoice.of_company(@company).take(2)
    invoice1.update_columns(state: :accepted, number: 'FS-10/08/2022/RS')
    invoice2.update_columns(state: :accepted, number: 'FSK/20/09/2022/RSK')
    invoice1.invoice_document&.destroy
    invoice2.invoice_document&.destroy

    assert_difference('InvoiceDocument.count', 2) do
      subject.call(Time.zone.today)
    end
    assert_equal 'FS_10_08_2022_RS.pdf', invoice1.reload.invoice_document.document.original_filename
    assert_equal 'FSK_20_09_2022_RSK.PDF', invoice2.reload.invoice_document.document.original_filename
  end

  test 'does not create invoice document if it has already been created' do
    invoice = invoices(:payment_three)
    invoice_document = invoice.invoice_document
    invoice.update_columns(state: :accepted, number: 'FS-10/08/2022/RS')

    assert invoice_document.present?
    assert_no_difference('InvoiceDocument.count') do
      assert_no_changes -> { invoice_document.reload.document.id } do
        subject.call(Time.zone.today)
      end
    end
    assert_equal invoice_document, invoice.reload.invoice_document
  end
end
