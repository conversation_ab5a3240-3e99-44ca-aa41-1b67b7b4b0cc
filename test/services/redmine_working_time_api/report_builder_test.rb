require 'test_helper'
require_relative './support/builder_test_helper'

module RedmineWorkingTimeApi
  class ReportBuilderTest < ActiveSupport::TestCase
    include BuilderTestHelper

    subject do
      # required work hours for 2/2020 => 20 * 7.5 = 150
      result = RedmineWorkingTimeApi::ReportBuilder.generate_summary(2020, 2, companies(:one))
      workbook_to_hash(result)
    end

    setup do
      User.update_all(created_at: Date.new(2020, 2, 1), activates_on: Date.new(2020, 2, 1))
    end

    describe 'xls body' do
      before do
        user = users(:milosz)
        project = projects(:two)

        stub_api_request([
          {
            id: 1, project: { identifier: project.identifier },
            user: { login: user.username }, hours: 160
          },
        ])
      end

      it 'returns proper values' do
        record = subject.first

        expect(record).must_equal({
          "Kod" => "MGRABOWSKI",
          "ID" => users(:milosz).id,
          "Nazwisko" => "<PERSON>rabowski",
          "<PERSON><PERSON>" => "<PERSON><PERSON><PERSON><PERSON>",
          "PozycjaZestawienia" => 0,
          "DataOd" => Date.new(2020, 2, 1),
          "DataDo" => Date.new(2020, 2, 29),
          "CzasPracyGodz" => "160:00",
          "CzasPracyDni" => 20,
          "Nadgodziny50" => "0:00",
          "Nadgodziny100" => "0:00",
          "GodzinyNocne" => "0:00",
          "Strefa" => "praca.pdst",
          "Wydzial" => "119",
          "Wydzial_adres_wezla" => nil,
          "Projekt" => 1,
          "Projekt_adres_wezla" => nil
        })
      end
    end

    describe 'when total reported time exceeds time required in invoicing month' do
      describe 'when all time is reported to a single project' do
        before do
          user = users(:milosz)
          project = projects(:two)

          stub_api_request([
            {
              id: 1, project: { identifier: project.identifier },
              user: { login: user.username }, hours: 170
            },
          ])
        end

        it 'returns single row' do
          expect(subject.count).must_equal(1)
        end

        it 'includes all hours reported' do
          expect(subject.first['CzasPracyGodz']).must_equal("170:00")
        end
      end

      describe 'when time is reported to multiple projects' do
        before do
          user = users(:milosz)
          project_one = projects(:one)
          project_two = projects(:two)

          stub_api_request([
            {
              id: 1, project: { identifier: project_one.identifier },
              user: { login: user.username }, hours: 70
            },
            {
              id: 2, project: { identifier: project_two.identifier },
              user: { login: user.username }, hours: 100
            }
          ])
        end

        it 'returns one row' do
          expect(subject.count).must_equal(1)
        end

        it 'includes all hours reported per project' do
          row = subject.first
          expect(row['CzasPracyGodz']).must_equal('170:00')
        end
      end
    end

    describe 'when total reported time is equal time required in invoicing month' do
      describe 'when all time is reported to a single project' do
        before do
          user = users(:milosz)
          project = projects(:two)

          stub_api_request([
            {
              id: 1, project: { identifier: project.identifier },
              user: { login: user.username }, hours: 160
            },
          ])
        end

        it 'returns single row' do
          expect(subject.count).must_equal(1)
        end

        it 'includes all hours reported' do
          expect(subject.first['CzasPracyGodz']).must_equal("160:00")
        end
      end

      describe 'when time is reported to multiple projects' do
        before do
          user = users(:milosz)
          project_one = projects(:one)
          project_two = projects(:two)

          stub_api_request([
            {
              id: 1, project: { identifier: project_one.identifier },
              user: { login: user.username }, hours: 60
            },
            {
              id: 2, project: { identifier: project_two.identifier },
              user: { login: user.username }, hours: 100
            },
          ])
        end

        it 'returns one row' do
          expect(subject.count).must_equal(1)
        end

        it 'includes all hours reported per project' do
          row = subject.first
          expect(row['CzasPracyGodz']).must_equal('160:00')
        end
      end
    end

    describe 'when total reported time is less than time required in invoicing month' do
      before do
        user = users(:milosz)
        project_one = projects(:one)
        project_two = projects(:two)

        stub_api_request([
          {
            id: 1, project: { identifier: project_one.identifier },
            user: { login: user.username }, hours: 20
          },
          {
            id: 2, project: { identifier: project_two.identifier },
            user: { login: user.username }, hours: 100
          },
        ])
      end

      it 'returns two rows' do
        expect(subject.count).must_equal(2)
      end

      it 'assigns missing amount to the placeholder project' do
        first_row, placeholder_row = subject
        expect(first_row['CzasPracyGodz']).must_equal('120:00')
        expect(placeholder_row['CzasPracyGodz']).must_equal('40:00')
      end

      it 'returns proper accounting number for placeholder project row' do
        placeholder_row = subject.last
        expect(placeholder_row['Projekt']).must_equal(projects(:artegence_wew).accounting_number.number)
      end

      context 'when part_time user' do
        before { users(:milosz).update(part_time: true) }

        it 'returns one row' do
          expect(subject.count).must_equal(1)
        end

        it 'includes all hours reported per project' do
          row = subject.first
          expect(row['CzasPracyGodz']).must_equal('120:00')
        end
      end
    end

    describe 'when multiple users are included in report' do
      let(:project_one) { projects(:one) }
      let(:project_two) { projects(:mkalita_project_alternative) }
      let(:user_one) { users(:milosz) }
      let(:user_two) do
        user_one.dup.tap do |u|
          u.username = 'kkapcioch'
          u.first_name = 'Krzysztof'
          u.last_name = 'Kapcioch'
          u.email = '<EMAIL>'
          u.uid = 6666
          u.created_at = Date.new(2020, 2, 1)
          u.activates_on = Time.zone.today
          u.save!
          u.update_columns(activates_on: Date.new(2020, 2, 1))
          u.user_contracts.create!(agreement_type: :employment, starts_on: Date.new(2020, 2, 1),
                                   month_notice_period: 1)
        end
      end

      before do
        stub_api_request([
                           {
                             id: 1, project: { identifier: project_one.identifier },
                             user: { login: user_one.username }, hours: 60
                           },
                           {
                             id: 2, project: { identifier: project_one.identifier },
                             user: { login: user_one.username }, hours: 100
                           },
                           {
                             id: 3, project: { identifier: project_one.identifier },
                             user: { login: user_two.username }, hours: 20
                           },
                           {
                             id: 4, project: { identifier: project_two.identifier },
                             user: { login: user_two.username }, hours: 100
                           }
                         ])
      end

      it 'returns properly grouped reports' do
        result = subject
        expect(result.count).must_equal(4)

        assert result.any? do |row|
          row['Kod'] == 'MGRABOWSKI' && row['CzasPracyGodz'] == '160:00' && row['Projekt'] == 1
        end

        assert result.any? do |row|
          row['Kod'] == 'KKAPCIOCH' && row['CzasPracyGodz'] == '20:00' && row['Projekt'] == 1
        end

        assert result.any? do |row|
          row['Kod'] == 'KKAPCIOCH' && row['CzasPracyGodz'] == '100:00' && row['Projekt'] == 2
        end

        assert result.any? do |row|
          row['Kod'] == 'KKAPCIOCH' && row['CzasPracyGodz'] == '40:00' && row['Projekt'] == 3
        end
      end
    end

    describe 'while multiple requests' do
      let(:project_one) { projects(:one) }
      let(:project_two) { projects(:mkalita_project_alternative) }
      let(:user_one) { users(:milosz) }
      let(:user_two) do
        user_one.dup.tap do |u|
          u.username = 'kkapcioch'
          u.first_name = 'Krzysztof'
          u.last_name = 'Kapcioch'
          u.email = '<EMAIL>'
          u.uid = 6666
          u.save!(validate: false)
          u.user_contracts.create!(agreement_type: :employment, starts_on: Date.new(2018, 2, 1),
                                   month_notice_period: 1)
        end
      end

      it 'generates properly' do
        stub_api_request(
          [
            {
              id: 1, project: { identifier: project_one.identifier },
              user: { login: user_one.username }, hours: 60
            },
            {
              id: 2, project: { identifier: project_one.identifier },
              user: { login: user_one.username }, hours: 100
            },
            {
              id: 3, project: { identifier: project_one.identifier },
              user: { login: user_two.username }, hours: 20
            },
            {
              id: 4, project: { identifier: project_two.identifier },
              user: { login: user_two.username }, hours: 100
            }
          ], total_count: 8
        )

        assert_nothing_raised { subject }
      end
    end
  end
end
