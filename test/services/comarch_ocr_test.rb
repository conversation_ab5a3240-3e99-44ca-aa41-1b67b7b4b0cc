require 'test_helper'

class ComarchOcrTest < ActiveSupport::TestCase
  let(:credentials) { Settings.comarch_ocr_api.credentials }
  let(:file_path) { 'test/fixtures/files/sample.pdf' }

  setup { Settings.comarch_ocr_api.enabled = true }
  teardown { Settings.comarch_ocr_api.enabled = false }

  it 'processes invoice' do
    auth_token = 'auth_token'
    invoice_data = {
      Data: Base64.strict_encode64(File.read(file_path)),
      Filename: 'sample.pdf',
      RecognitionLanguageOption: 0,
      OneInvoiceFile: 1,
      PaymentMethodMapping: 1
    }
    @recognized_data = {
      Fields: {
        DocumentNumber: 'FV/1/09/2022',
        DateOfIssue: '2022-09-30',
        DateOfSale: '2022-09-30',
        DueDate: '2022-10-14',
        PaymentForm: 'przelew',
        BankAccountNumber: 'BankAccountNumber'
      },
      IsCorrection: false,
      SellerContractor: {
        TIN: 'TIN',
        CompanyName: 'CompanyName',
        Street: 'Street',
        StreetNumber: 'StreetNumber',
        ApartmentsNumber: '',
        PostCode: 'PostCode',
        PostOffice: '',
        City: 'Warszawa',
        Voivodeship: 'MAZOWIECKIE',
        ActiveVATTaxpayer: true
      },
      BuyerContractor: {
        TIN: '**********',
        CompanyName: 'EFIGENCE SPÓŁKA AKCYJNA',
        Street: 'ul. Wołoska',
        StreetNumber: '9 A',
        ApartmentsNumber: '',
        PostCode: '02-583',
        PostOffice: 'Warszawa',
        City: 'Warszawa',
        Voivodeship: 'MAZOWIECKIE',
        ActiveVATTaxpayer: true
      },
      Currency: 'PLN',
      Language: 'PL',
      VatPositions: [{ VatRate: 23, VatStatus: 0, Subtotal: 250.0, VAT: 57.5, Total: 307.5 }],
      PageProperties: { FirstPageNumber: 1, DocumentLength: 1, PageRotation: 0 },
      ProductItems: [
        {
          Name: 'Usługi dodatkowe',
          Unit: 'szt',
          Count: 1.0,
          NettoUnitPrice: 250.0,
          BruttoUnitPrice: 307.5,
          Netto: 250.0,
          Brutto: 307.5,
          VatRate: 23.0,
          VatAmount: 57.5
        }
      ],
      Barcodes: [],
      NumberOfPagesLeft: 2412
    }
    response_body = {
      ExportString: [@recognized_data].to_json,
      Message: 'Rozpoznano dokument sample.pdf.',
      Code: 1,
      Status: 1,
      AdditionalMessage: '',
      UriToDownload: ''
    }

    stub_request(:post, "#{Settings.comarch_ocr_api.host}/v1.0/api/sessions/post")
      .with(
          headers: {
            'Content-Type' => 'application/json',
            'authKey' => credentials[:authKey],
            'authSecret' => credentials[:authSecret]
          }
        )
      .to_return(body: { sessionToken: auth_token }.to_json, status: 200)

    stub_request(:post, "#{Settings.comarch_ocr_api.host}/v1.0/api/invoice/post")
      .with(
          headers: {
            'Content-Type' => 'application/json',
            'authToken' => auth_token
          },
          body: invoice_data.to_json
        )
      .to_return(body: response_body.to_json, status: 200)

    assert_nothing_raised do
      result = ComarchOcr.process_invoice(File.new(file_path))

      assert_equal @recognized_data.with_indifferent_access, result
    end
  end

  it 'raises AuthorizationError after 3 unsuccessful tries' do
    ComarchOcr.instance_variable_set(:@auth_token, nil)
    stub_request(:post, "#{Settings.comarch_ocr_api.host}/v1.0/api/sessions/post")
      .to_return(status: 401, body: { Code: 14, Status: 3 }.to_json)
      .to_return(status: 401, body: { Code: 14, Status: 3 }.to_json)
      .to_return(status: 401, body: { Code: 14, Status: 3 }.to_json)

    assert_raises ComarchOcr::AuthorizationError do
      ComarchOcr.process_invoice(File.new(file_path))
    end
  end
end
