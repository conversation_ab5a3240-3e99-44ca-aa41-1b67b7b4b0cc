require 'test_helper'

class UnpaidInvoicesXlsxReaderSimpleTest < ActiveSupport::TestCase
  setup do
    @client = clients(:arte)
    @client.update!(vat_number: '1234567890', invoice_sending_email: '<EMAIL>')
    
    # Mock the mailer to avoid rendering templates
    mock_mail = mock('mail')
    mock_mail.stubs(:html_part).returns(mock('html_part', body: mock('body', raw_source: '<p>Test email content</p>')))
    InvoicesMailer.stubs(:unpaid_invoices_to_five).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_six_to_thirty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_thirty_one_to_sixty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_sixty_one_to_one_twenty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_over_one_twenty).returns(mock_mail)
  end

  test 'groups invoices by client and overdue status' do
    # Create a mock file
    mock_file = mock('file')
    mock_file.stubs(:path).returns('/tmp/test.xlsx')
    
    # Create reader instance and mock the internal methods
    reader = UnpaidInvoicesXlsxReader.allocate
    reader.instance_variable_set(:@start_row, 7)
    reader.instance_variable_set(:@columns, {
      company: 0, vat: 2, invoice_number: 3, currency: 6, is_paid: 7,
      to_five: 9, six_to_thirty: 11, end_sum: 19
    })
    
    # Mock the raw data extraction
    raw_data = [
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-001',
        amount: 1000.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      },
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-002',
        amount: 500.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      }
    ]
    
    reader.stubs(:extract_raw_data).returns(raw_data)
    
    result = reader.read
    
    assert_equal 1, result.length
    
    grouped_data = result.first
    assert_equal 'Artegence', grouped_data[:client]
    assert_equal 'unpaid_invoices_to_five', grouped_data[:overdue_status]
    assert_equal 1500.0, grouped_data[:amount]
    assert_equal ['INV-001', 'INV-002'], grouped_data[:invoices_numbers]
    assert_equal '<EMAIL>', grouped_data[:emails]
    assert grouped_data[:checked]
    assert_not_nil grouped_data[:content]
  end

  test 'creates separate groups for different overdue statuses' do
    mock_file = mock('file')
    mock_file.stubs(:path).returns('/tmp/test.xlsx')
    
    reader = UnpaidInvoicesXlsxReader.allocate
    reader.instance_variable_set(:@start_row, 7)
    reader.instance_variable_set(:@columns, {
      company: 0, vat: 2, invoice_number: 3, currency: 6, is_paid: 7,
      to_five: 9, six_to_thirty: 11, end_sum: 19
    })
    
    raw_data = [
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-001',
        amount: 500.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      },
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-002',
        amount: 300.0,
        overdue_status: 'unpaid_invoices_six_to_thirty',
        company: 'Test Company',
        currency: 'PLN'
      }
    ]
    
    reader.stubs(:extract_raw_data).returns(raw_data)
    
    result = reader.read
    
    assert_equal 2, result.length
    
    to_five_group = result.find { |r| r[:overdue_status] == 'unpaid_invoices_to_five' }
    six_to_thirty_group = result.find { |r| r[:overdue_status] == 'unpaid_invoices_six_to_thirty' }
    
    assert_not_nil to_five_group
    assert_not_nil six_to_thirty_group
    
    assert_equal 500.0, to_five_group[:amount]
    assert_equal ['INV-001'], to_five_group[:invoices_numbers]
    
    assert_equal 300.0, six_to_thirty_group[:amount]
    assert_equal ['INV-002'], six_to_thirty_group[:invoices_numbers]
  end

  test 'handles multiple clients' do
    @client2 = clients(:polexit)
    @client2.update!(vat_number: '9876543210', invoice_sending_email: '<EMAIL>')
    
    mock_file = mock('file')
    mock_file.stubs(:path).returns('/tmp/test.xlsx')
    
    reader = UnpaidInvoicesXlsxReader.allocate
    reader.instance_variable_set(:@start_row, 7)
    reader.instance_variable_set(:@columns, {
      company: 0, vat: 2, invoice_number: 3, currency: 6, is_paid: 7,
      to_five: 9, six_to_thirty: 11, end_sum: 19
    })
    
    raw_data = [
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-001',
        amount: 500.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      },
      {
        client: 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego',
        email: '<EMAIL>',
        invoice_number: 'INV-002',
        amount: 300.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      }
    ]
    
    reader.stubs(:extract_raw_data).returns(raw_data)
    
    result = reader.read
    
    assert_equal 2, result.length
    
    # Results should be sorted by client name
    assert_equal 'Artegence', result[0][:client]
    assert_equal 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego', result[1][:client]
  end

  test 'filters out invoices without overdue status' do
    mock_file = mock('file')
    mock_file.stubs(:path).returns('/tmp/test.xlsx')
    
    reader = UnpaidInvoicesXlsxReader.allocate
    reader.instance_variable_set(:@start_row, 7)
    reader.instance_variable_set(:@columns, {
      company: 0, vat: 2, invoice_number: 3, currency: 6, is_paid: 7,
      to_five: 9, six_to_thirty: 11, end_sum: 19
    })
    
    raw_data = [
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-001',
        amount: 500.0,
        overdue_status: nil, # No overdue status
        company: 'Test Company',
        currency: 'PLN'
      },
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-002',
        amount: 300.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      }
    ]
    
    reader.stubs(:extract_raw_data).returns(raw_data)
    
    result = reader.read
    
    assert_equal 1, result.length
    assert_equal ['INV-002'], result.first[:invoices_numbers]
  end

  test 'handles empty email addresses' do
    @client.update!(invoice_sending_email: '')
    
    mock_file = mock('file')
    mock_file.stubs(:path).returns('/tmp/test.xlsx')
    
    reader = UnpaidInvoicesXlsxReader.allocate
    reader.instance_variable_set(:@start_row, 7)
    reader.instance_variable_set(:@columns, {
      company: 0, vat: 2, invoice_number: 3, currency: 6, is_paid: 7,
      to_five: 9, six_to_thirty: 11, end_sum: 19
    })
    
    raw_data = [
      {
        client: 'Artegence',
        email: '',
        invoice_number: 'INV-001',
        amount: 500.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      }
    ]
    
    reader.stubs(:extract_raw_data).returns(raw_data)
    
    result = reader.read
    
    assert_equal 1, result.length
    assert_equal '', result.first[:emails]
    assert_not result.first[:checked]
  end

  test 'sorts results correctly' do
    @client2 = clients(:polexit)
    @client2.update!(vat_number: '9876543210', invoice_sending_email: '<EMAIL>')
    
    mock_file = mock('file')
    mock_file.stubs(:path).returns('/tmp/test.xlsx')
    
    reader = UnpaidInvoicesXlsxReader.allocate
    reader.instance_variable_set(:@start_row, 7)
    reader.instance_variable_set(:@columns, {
      company: 0, vat: 2, invoice_number: 3, currency: 6, is_paid: 7,
      to_five: 9, six_to_thirty: 11, end_sum: 19
    })
    
    raw_data = [
      {
        client: 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego',
        email: '<EMAIL>',
        invoice_number: 'INV-003',
        amount: 200.0,
        overdue_status: 'unpaid_invoices_six_to_thirty',
        company: 'Test Company',
        currency: 'PLN'
      },
      {
        client: 'Artegence',
        email: '<EMAIL>',
        invoice_number: 'INV-001',
        amount: 500.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      },
      {
        client: 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego',
        email: '<EMAIL>',
        invoice_number: 'INV-004',
        amount: 100.0,
        overdue_status: 'unpaid_invoices_to_five',
        company: 'Test Company',
        currency: 'PLN'
      }
    ]
    
    reader.stubs(:extract_raw_data).returns(raw_data)
    
    result = reader.read
    
    assert_equal 3, result.length
    
    # Should be sorted by client name first, then by overdue status order
    assert_equal 'Artegence', result[0][:client]
    assert_equal 'unpaid_invoices_to_five', result[0][:overdue_status]
    
    assert_equal 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego', result[1][:client]
    assert_equal 'unpaid_invoices_to_five', result[1][:overdue_status]
    
    assert_equal 'P.P.H.U. Polexit sprzedaż skup buraka cukrowego', result[2][:client]
    assert_equal 'unpaid_invoices_six_to_thirty', result[2][:overdue_status]
  end
end
