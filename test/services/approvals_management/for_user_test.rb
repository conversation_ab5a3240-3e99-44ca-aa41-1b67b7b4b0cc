require 'test_helper'

class ApprovalsManagement::ForUserTest < ActiveSupport::TestCase
  include <PERSON><PERSON><PERSON><PERSON>

  def subject(user)
    ApprovalsManagement::ForUser.new(user)
  end

  before do
    Sidekiq::Testing.fake!
    @agreement = agreements(:main)
    User.any_instance.stubs(:update_approvals_cache).returns(nil)
  end

  test 'approval should be created if user matches company, department and contract type' do
    user = generate_user

    assert_includes @agreement.companies, user.company
    assert_includes @agreement.departments, user.department
    assert_not user.contract_of_employment
    assert @agreement.business_to_business

    assert_difference('Approval.count', 1) do
      subject(user).call
      assert_equal 1, user.approvals.count
    end
  end

  test 'approval should not be created if user is inactive' do
    user = generate_user
    user.update(state: :locked)

    assert_no_difference('Approval.count') do
      subject(user).call
    end
  end

  test 'approval should NOT be created if user matches department, contract type but not the company' do
    user = generate_users(1, company: companies(:one)).first

    assert_not_includes @agreement.companies, user.company
    assert_includes @agreement.departments, user.department
    assert_not user.contract_of_employment
    assert @agreement.business_to_business

    assert_no_difference('Approval.count') do
      subject(user).call
    end
  end

  test 'approval should NOT be created if user matches company, contract type but not the department' do
    user = generate_users(1, department: departments(:mkalita_department)).first

    assert_includes @agreement.companies, user.company
    assert_not_includes @agreement.departments, user.department
    assert_not user.contract_of_employment
    assert @agreement.business_to_business

    assert_no_difference('Approval.count') do
      subject(user).call
    end
  end

  test 'approval should be destroyed if user company no longer matches agreement' do
    user = generate_user

    assert_difference('Approval.count', 1) do
      subject(user).call
    end

    user.company = companies(:mkalita_company)
    user.save!

    assert_difference('Approval.count', -1) do
      subject(user).call
    end
  end

  test 'approval should be destroyed if user department no longer matches agreement' do
    user = generate_user

    assert_difference('Approval.count', 1) do
      subject(user).call
    end

    user.department = departments(:mkalita_department)
    user.save!

    assert_difference('Approval.count', -1) do
      subject(user).call
    end
  end

  test 'approval should be destroyed if user contract type no longer matches agreement' do
    user = generate_user

    assert_difference('Approval.count', 1) do
      subject(user).call
    end

    user.contract_of_employment = true
    user.save!

    assert_difference('Approval.count', -1) do
      subject(user).call
    end
  end

  test 'approval should stay untouched in case of no agreement related changes on user' do
    user = generate_user

    assert_difference('Approval.count', 1) do
      subject(user).call
    end

    assert_equal 1, user.approvals.count

    approval = user.approvals.first

    user.save!

    assert_no_difference('Approval.count') do
      subject(user).call

      assert_equal 1, user.approvals.count
      assert_equal approval, user.approvals.first
    end
  end
end
