require 'test_helper'

class RedmineIssuesImporterTest < ActiveSupport::TestCase
  def setup
    @importer = RedmineIssuesImporter.new
  end

  test 'limit adds limit to options' do
    @importer.limit(10)
    assert_equal 10, @importer.instance_variable_get(:@options)[:limit]
  end

  test 'import returns all projects' do
    response = { 'total_count' => 2, 'issues' => [{ 'id' => 1, 'title' => 'Issue 1' },
                                                  { 'id' => 2, 'title' => 'Issue 2' }] }
    stub_request(:get, "#{Settings.redmine_api.uri}/issues.json")
      .with(query: hash_including('limit'))
      .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

    issues = @importer.import

    assert_equal 2, issues.size
    assert_equal [1, 2], issues.map { |p| p['id'] }
  end

  test 'import fetches projects in batches if there are more projects than the limit' do
    response = {
      'total_count' => 5,
      'issues' => [{ 'id' => 1, 'title' => 'Issue 1' }, { 'id' => 2, 'title' => 'Issue 2' }] }
    stub_request(:get, "#{Settings.redmine_api.uri}/issues.json")
      .with(query: hash_including('limit'))
      .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

    @importer.limit(2).import

    assert_requested :get, "#{Settings.redmine_api.uri}/issues.json?limit=2&offset=0", times: 1
    assert_requested :get, "#{Settings.redmine_api.uri}/issues.json?limit=2&offset=2", times: 1
    assert_requested :get, "#{Settings.redmine_api.uri}/issues.json?limit=2&offset=4", times: 1
  end
end
