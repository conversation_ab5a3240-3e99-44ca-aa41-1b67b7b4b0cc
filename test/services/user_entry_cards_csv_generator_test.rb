class UserEntryCardsCsvGeneratorTest < ActiveSupport::TestCase
  test 'generates csv properly' do
    csv = UserEntryCardsCsvGenerator.generate
    user_entry_cards = UserEntryCard.order(:user_id, :id)

    array = CSV.parse(csv)
    assert_equal ['ID karty', 'ID Użytkownika', 'Data przypisania karty do użytkownika',
                  'Data odpięcia karty od użytkownika'],
                 array.first

    user_entry_cards.each_with_index do |card, index|
      assert_equal [card.card_number.to_s, card.user_id.to_s, card.starts_on.to_s,
                    card.ends_on.to_s.presence],
                   array[index + 1]
    end
  end
end
