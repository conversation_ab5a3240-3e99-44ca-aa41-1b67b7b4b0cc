require 'test_helper'

# tail -n 20000 -f log/test.log | grep -A55 "ProjectPolicyTest: test_create"
class DepartmentPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { Department }
  let(:nil_user) do
    nil
  end
  let(:admin_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'admin_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_admin)
    user
  end
  let(:record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'record_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company_id: companies(:one).id)
    user.global_roles << global_roles(:global_hr_manager)
    user
  end
  let(:regular_record) do
    record_model.create(
      name: 'bla',
      chief_id: record_user.id,
      company_id: companies(:one).id
    )
  end
  let(:not_record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'not_record_user_gmail',
                       first_name: '<PERSON>',
                       last_name: 'Doe',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_hr_manager)
    user.reload
  end

  def test_index
    assert_permit admin_user, record_model
    assert_permit record_user, record_model
    assert_permit not_record_user, record_model
    refute_permit nil_user, record_model
  end

  def test_show
    assert_permit admin_user, regular_record
    assert_permit record_user, regular_record
    assert_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end
end
