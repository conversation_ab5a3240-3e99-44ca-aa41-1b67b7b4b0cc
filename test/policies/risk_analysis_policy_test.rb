require 'test_helper'

class RiskAnalysisPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { RiskAnalysis }
  let(:risk_analysis) { risk_analyses(:one) }

  let(:nil_user) { nil }
  let(:admin_user) { users(:mkalita_user) }
  let(:regular_user) { users(:milosz) }

  def test_index
    assert_permit admin_user, record_model, :index?
    refute_permit regular_user, record_model, :index?
    refute_permit nil_user, record_model, :index?
  end

  def test_show
    assert_permit admin_user, risk_analysis, :show?
    refute_permit regular_user, risk_analysis, :show?
    refute_permit nil_user, risk_analysis, :show?
  end

  def test_create
    assert_permit admin_user, risk_analysis, :create?
    refute_permit regular_user, risk_analysis, :create?
    refute_permit nil_user, risk_analysis, :create?
  end

  def test_update
    assert_permit admin_user, risk_analysis, :update?
    refute_permit regular_user, risk_analysis, :update?
    refute_permit nil_user, risk_analysis, :update?
  end

  def test_destroy
    assert_permit admin_user, risk_analysis, :destroy?
    refute_permit regular_user, risk_analysis, :destroy?
    refute_permit nil_user, risk_analysis, :destroy?
  end

  def test_create_risk_analysis_item
    assert_permit admin_user, risk_analysis, :create_risk_analysis_item?
    refute_permit regular_user, risk_analysis, :create_risk_analysis_item?
    refute_permit nil_user, risk_analysis, :create_risk_analysis_item?
  end

  def test_update_risk_analysis_item
    assert_permit admin_user, risk_analysis, :update_risk_analysis_item?
    refute_permit regular_user, risk_analysis, :update_risk_analysis_item?
    refute_permit nil_user, risk_analysis, :update_risk_analysis_item?
  end

  def test_destroy_risk_analysis_item
    assert_permit admin_user, risk_analysis, :destroy_risk_analysis_item?
    refute_permit regular_user, risk_analysis, :destroy_risk_analysis_item?
    refute_permit nil_user, risk_analysis, :destroy_risk_analysis_item?
  end
end
