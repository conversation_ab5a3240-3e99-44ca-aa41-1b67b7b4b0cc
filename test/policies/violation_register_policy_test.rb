require 'test_helper'

class ViolationRegisterPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { ViolationRegister }
  let(:regular_record) { violation_registers(:one) }

  let(:nil_user) { nil }
  let(:admin_user) { users(:mkalita_user) }
  let(:regular_user) { users(:milosz) }

  def test_index
    assert_permit admin_user, record_model
    refute_permit regular_user, record_model
    refute_permit nil_user, record_model
  end

  def test_show
    assert_permit admin_user, regular_record
    refute_permit regular_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_new
    assert_permit admin_user, record_model
    refute_permit regular_user, record_model
    refute_permit nil_user, record_model
  end

  def test_create
    assert_permit admin_user, record_model
    refute_permit regular_user, record_model
    refute_permit nil_user, record_model
  end

  def test_update
    assert_permit admin_user, regular_record
    refute_permit regular_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_destroy
    assert_permit admin_user, regular_record
    refute_permit regular_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_processing
    assert_permit admin_user, regular_record
    refute_permit regular_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_reported
    assert_permit admin_user, regular_record
    refute_permit regular_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_complete
    assert_permit admin_user, regular_record
    refute_permit regular_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_attachment
    assert_permit admin_user, regular_record
    refute_permit regular_user, regular_record
    refute_permit nil_user, regular_record
  end
end
