require 'test_helper'

# tail -n 20000 -f log/test.log | grep -A55 "ProjectPolicyTest: test_create"
class NotificationPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { Notification }
  let(:nil_user) do
    nil
  end
  let(:admin_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'admin_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_admin)
    user
  end
  let(:record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'record_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_user)
    user
  end
  let(:regular_record) do
    record_model.create(
      title: 'bla',
      details: 'bla',
      user_id: record_user.id,
      state: :sent,
      last_sent_at: Time.zone.parse('2014-01-02').to_date,
      subject_id: holiday_requests(:one).id,
      subject_type: holiday_requests(:one).class.name
    )
  end
  let(:not_record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'not_record_user_gmail',
                       first_name: 'John',
                       last_name: 'Doe',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_user)
    user.reload
  end

  let(:my_record) do
    my_record = record_model.create_with(
      state: 'sent',
      title: 'my bla'
    ).find_or_create_by!(
      user_id: record_user.id,
      subject_id: holiday_requests(:two).id,
      subject_type: holiday_requests(:two).class.name
    )
  end

  def test_scope_of_me
    refute_equal [regular_record, my_record], "::#{record_model}Policy::Scope".constantize.new(admin_user, record_model.all).resolve.to_a
    assert_equal [regular_record, my_record], "::#{record_model}Policy::Scope".constantize.new(record_user, record_model.all).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(not_record_user, record_model.all).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(nil_user, record_model.all).resolve.to_a
  end

  def test_scope
    refute_equal Notification.all.to_a, "::#{record_model}Policy::Scope".constantize.new(admin_user, record_model.all).resolve.to_a
    assert_equal [regular_record], "::#{record_model}Policy::Scope".constantize.new(record_user, record_model.all).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(not_record_user, record_model.all).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(nil_user, record_model.all).resolve.to_a
  end

  def test_index
    assert_permit admin_user, record_model
    assert_permit record_user, record_model
    assert_permit not_record_user, record_model
    refute_permit nil_user, record_model
  end

  def test_show
    refute_permit admin_user, regular_record
    assert_permit record_user, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end
end
