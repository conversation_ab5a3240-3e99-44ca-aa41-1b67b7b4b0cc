require 'test_helper'

# tail -n 20000 -f log/test.log | grep -A55 "ProjectPolicyTest: test_create"
class HolidayRequestPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { HolidayRequest }
  let(:nil_user) do
    nil
  end
  let(:admin_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'admin_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company_id: companies(:mkalita_company).id)
    user.global_roles << global_roles(:global_admin)
    user
  end
  let(:record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'record_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company_id: companies(:mkalita_company).id)
    user.global_roles << global_roles(:global_user)
    user.department = departments(:mkalita_department)
    user
  end
  let(:traffic_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'traffic_user_gmail',
                       first_name: '<PERSON>k',
                       last_name: 'Sienkiewicz',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company_id: companies(:mkalita_company).id)
    user.global_roles << global_roles(:global_role_global_traffic)
    user.department = departments(:mkalita_department)
    user
  end
  let(:regular_record) do
    holiday_request = holiday_requests(:two)
    record_model.create(
      accepted_at: nil,
      applicant_comment: holiday_request.applicant_comment,
      applicant_id: record_user.id,
      category: holiday_request.category,
      ends_on: Time.zone.parse('2014-01-02').to_date,
      examiner_comment: holiday_request.examiner_comment,
      examiner_id: holiday_request.examiner_id,
      rejected_at: nil,
      starts_on: Time.zone.parse('2014-01-02').to_date,
      visible: holiday_request.visible,
      created_by_user_id: holiday_request.applicant.id,
      updated_by_user_id: holiday_request.applicant.id,
      modified_by_user_at: Time.zone.parse('2014-01-02').beginning_of_day
    )
  end
  let(:not_record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'not_record_user_gmail',
                       first_name: 'John',
                       last_name: 'Doe',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company_id: companies(:mkalita_company).id)
    user.global_roles << global_roles(:global_user)
    user.reload
  end

  # FIXME: !!!
  let(:scope_of_department) { record_model.all } # { record_model.of_department('Xyz') }

  let(:department_record) do
    record_model.create!(date: '1999-02-02',
                         applicant_id: users(:mkalita_user).id)
  end

  def test_scope_of_department
    HolidayRequest.where.not(id: regular_record.id).destroy_all
    # TODO: szef dzialu
    # assert_equal [regular_record, department_record], "::#{record_model}Policy::Scope".constantize.new(admin_user, scope_of_department).resolve.to_a
    assert_equal [regular_record], "::#{record_model}Policy::Scope".constantize.new(record_user, scope_of_department).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(not_record_user, scope_of_department).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(nil_user, scope_of_department).resolve.to_a
  end

  def test_scope
    HolidayRequest.where.not(id: regular_record.id).destroy_all
    assert_equal HolidayRequest.all.to_a, "::#{record_model}Policy::Scope".constantize.new(admin_user, record_model.all).resolve.to_a
    assert_equal [regular_record], "::#{record_model}Policy::Scope".constantize.new(record_user, record_model.all).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(not_record_user, record_model.all).resolve.to_a
    assert_empty "::#{record_model}Policy::Scope".constantize.new(nil_user, record_model.all).resolve.to_a
  end

  def test_index
    assert_permit admin_user, record_model
    assert_permit record_user, record_model
    assert_permit not_record_user, record_model
    assert_permit traffic_user, record_model
    not_record_user.update_columns(company_id: nil)
    refute_permit not_record_user.reload, record_model
    refute_permit nil_user, record_model
  end

  def test_show
    assert_permit admin_user, regular_record
    assert_permit record_user, regular_record
    assert_permit traffic_user, record_model
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_create
    assert_permit admin_user, regular_record
    assert_permit record_user, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
    refute_permit traffic_user, record_model
  end

  def test_update
    assert_permit admin_user, regular_record
    assert_permit record_user, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
    refute_permit traffic_user, record_model
  end

  def test_destroy
    assert_permit admin_user, regular_record
    assert_permit record_user, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
    refute_permit traffic_user, record_model
  end
end
