require 'test_helper'

class InventoryItemPolicyTest < PolicyAssertions::Test
  let(:record_model) { InventoryItem }
  let(:regular_record) { inventory_items(:dell) }

  let(:regular_user) { users(:internal_user) }
  let(:admin_user) { users(:mkalita_user) }

  def test_index
    refute_permit regular_user, record_model
    assert_permit admin_user, record_model
  end

  def test_show
    refute_permit regular_user, regular_record
    assert_permit admin_user, regular_record
  end

  def test_create
    refute_permit regular_user, record_model
    assert_permit admin_user, record_model
  end

  def test_update
    refute_permit regular_user, regular_record
    assert_permit admin_user, regular_record
  end

  def test_activate
    refute_permit regular_user, regular_record
    assert_permit admin_user, regular_record
  end

  def test_reject
    refute_permit regular_user, regular_record
    assert_permit admin_user, regular_record
  end

  def test_close
    refute_permit regular_user, regular_record
    assert_permit admin_user, regular_record
  end

  def test_scope
    assert_not_includes(scope(regular_user), regular_record)
    assert_includes(scope(admin_user), regular_record)
  end

  private

  def scope(user)
    InventoryItemPolicy::Scope.new(user, InventoryItem.all).resolve
  end
end
