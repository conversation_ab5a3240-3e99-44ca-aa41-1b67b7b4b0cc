require 'test_helper'

module B2B
  class WithdrawCostInvoiceInteractorTest < ActiveSupport::TestCase
    let(:cost_invoice) { cost_invoices(:mikolaj_cost_invoice) }
    let(:user) { users(:w<PERSON><PERSON>) }

    test 'withdraws cost invoice and creates snapshot' do
      assert_difference -> { ActiveSnapshot::Snapshot.count } do
        B2B::WithdrawCostInvoiceInteractor.call(cost_invoice: cost_invoice, user: user)
      end

      assert cost_invoice.reload.for_correction?
    end
  end
end
