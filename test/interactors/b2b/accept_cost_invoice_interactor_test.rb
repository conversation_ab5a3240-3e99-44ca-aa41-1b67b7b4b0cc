require 'test_helper'

module B2B
  class AcceptCostInvoiceInteractorTest < ActiveSupport::TestCase
    let(:cost_invoice) { cost_invoices(:mikolaj_cost_invoice) }
    let(:user) { users(:w<PERSON><PERSON>) }

    test 'call sends an email' do
      assert_difference -> { B2B::CostInvoiceNotificationMailer.deliveries.count } do
        B2B::AcceptCostInvoiceInteractor.call(cost_invoice: cost_invoice, user: user,
                                             force_accept: true)
      end

      mail = B2B::CostInvoiceNotificationMailer.deliveries.last
      assert_match('accepted', mail.subject)
    end
  end
end
