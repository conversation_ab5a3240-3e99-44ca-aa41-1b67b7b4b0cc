require 'test_helper'

module B2B
  class RejectCostInvoiceInteractorTest < ActiveSupport::TestCase
    let(:cost_invoice) do
      cost_invoices(:mikolaj_cost_invoice)
    end

    let(:user) { users(:w<PERSON><PERSON>) }

    test 'call sends an email' do
      assert_difference -> { B2B::CostInvoiceNotificationMailer.deliveries.count } do
        B2B::RejectCostInvoiceInteractor.call(cost_invoice: cost_invoice, user: user,
                                             force_accept: true)
      end

      mail = B2B::CostInvoiceNotificationMailer.deliveries.last
      assert_match('rejected', mail.subject)
    end
  end
end
