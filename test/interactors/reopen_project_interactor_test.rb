require 'test_helper'

class ReopenProjectInteractorTest < ActiveSupport::TestCase
  let(:project) { projects(:two) }

  test 'RedmineProjectsWorker is run upon project reopen' do
    assert_difference('RedmineProjectsWorker.jobs.count', 1) do
      result = ReopenProjectInteractor.call(project: project)
      assert result.success?
    end
    assert_equal ['reopen', project.id], RedmineProjectsWorker.jobs.last['args']
  end
end
