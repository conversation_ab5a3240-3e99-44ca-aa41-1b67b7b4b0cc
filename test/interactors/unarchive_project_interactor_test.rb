require 'test_helper'

class UnarchiveProjectInteractorTest < ActiveSupport::TestCase
  let(:project) { projects(:two) }

  test 'RedmineProjectsWorker is run upon project unarchivization' do
    project = projects(:two)
    assert_difference('RedmineProjectsWorker.jobs.count', 1) do
      result = UnarchiveProjectInteractor.call(project: project)
      assert result.success?
    end
    assert_equal ['unarchive', project.id], RedmineProjectsWorker.jobs.last['args']
  end

  test 'project gets unarchived but children remain archived' do
    project1 = projects(:mkalita_project)
    project2 = projects(:mkalita_project_child)

    ArchiveProjectInteractor.call(project: project1)
    UnarchiveProjectInteractor.call(project: project1.reload)

    assert_equal 'active', project1.reload.status
    assert_equal 'archived', project2.reload.status
  end
end
