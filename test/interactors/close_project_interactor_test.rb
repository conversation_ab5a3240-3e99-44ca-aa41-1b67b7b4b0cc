require 'test_helper'

class CloseProjectInteractorTest < ActiveSupport::TestCase
  let(:project) { projects(:two) }

  test 'RedmineProjectsWorker is run upon project close' do
    project.payment_schedule.payments.each(&:destroy)

    assert_difference('RedmineProjectsWorker.jobs.count', 1) do
      result = CloseProjectInteractor.call(project: project, overhead: false)
      assert result.success?
    end
    assert_equal ['close', project.id], RedmineProjectsWorker.jobs.last['args']
  end

  test 'fails if non issued payments are present' do
    result = CloseProjectInteractor.call(project: project, overhead: false)

    assert_not result.success?
    assert_not_empty result.message[:errors][:project]
  end

  test 'false if there are active assets' do
    user = users(:wiktoria)
    project.payment_schedule.payments.each(&:destroy)
    project.assets.where(type: 'Server').last.activate!(user)

    result = CloseProjectInteractor.call(project: project, overhead: false)

    assert_not result.success?
    assert_not_empty result.message[:errors][:project]
  end
end
