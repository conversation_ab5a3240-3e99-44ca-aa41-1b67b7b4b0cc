require 'test_helper'

module Api
  module V1
    # debug sql:
    # tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
    class UsersControllerTest < ActionController::TestCase
      include Minitest::XSwaggerSignInAs

      fixtures :global_roles, :user_global_roles, :users, :companies

      setup do
        @user = users(:mkalita_user)
        # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
        # HOWTO: test specific version
        # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
        # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
        # or
        # post '/humans',
        #      { human: { name: 'John', brain_type: 'small' } }.to_json,
        #      { 'Accept' => 'application/vnd.api+json; version=2',
        #        'Content-Type' => 'application/vnd.api+json; version=2' }
      end

      def test_rescues_module_inclusion
        assert_includes Api::V1::UsersController.ancestors, Api::V1::Concerns::Handlers::Rescues
      end

      test 'should get index with param collection_for_select and page paginated' do
        users_size = User.count
        get :index, format: :json, params: { per_page: 1, f: { collection_for_select: true } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
        assert_equal "<http://test.host/api/users?f%5Bcollection_for_select%5D=true&page=#{users_size}&per_page=1>; rel=\"last\", <http://test.host/api/users?f%5Bcollection_for_select%5D=true&page=2&per_page=1>; rel=\"next\"", response.headers['Link']
      end

      test 'should get index with param collection_for_select and page paginated page 2' do
        users_size = User.count
        user_on_first_page = User.order('id DESC').first!
        get :index, format: :json,
                    params: { per_page: 1, page: 2, f: { collection_for_select: true } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert json_body.last['id'].to_i > 0
        assert json_body.last['id'].to_i != user_on_first_page.id
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      def test_index_with_param_collection_for_select
        get :index, format: :json, params: { f: { collection_for_select: true } }
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, assigns(:users).size
        assert !json_body.first.include?('redmine_id')
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      def test_index_with_param_not_member_of_project
        get :index, format: :json,
                    params: { f: { collection_for_select: true,
                                   not_member_of_project: @user.memberships.first.project.id } }
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, User.all.size - 1
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      def test_index_with_term_filter
        get :index, format: :json, params: { f: { term: 'wiktoria' } }

        assert_response :success
        assert_equal 'whanowerska', json_body.first['username']
      end

      def test_index_with_multiword_term_filter
        get :index, format: :json, params: { f: { term: 'hanowerska wiktoria' } }

        assert_response :success
        assert_equal 'whanowerska', json_body.first['username']
      end

      def test_index_with_created_after_filter
        date = Date.new(2016, 5, 3)

        get :index, params: { f: { created_after: date } }, format: :json

        assert_response :success
        assert(json_body.detect { |user| user['id'] == users(:wiktoria).id })
        assert_not(json_body.detect { |user| user['id'] == users(:mikolaj).id })
      end

      def test_index_with_created_before_filter
        date = Date.new(2016, 5, 2)

        get :index, params: { f: { created_before: date } }, format: :json

        assert_response :success
        assert_not(json_body.detect { |user| user['id'] == users(:wiktoria).id })
        assert(json_body.detect { |user| user['id'] == users(:mikolaj).id })
      end

      def test_index_with_id_filter
        user = users(:wiktoria)

        get :index, params: { f: { user_id: user.id } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
        assert_equal user.id, json_body.first['id']
      end

      def test_index_with_departments_ids_filter
        department = departments(:two)

        get :index, params: { f: { departments_ids: department } }, format: :json

        assert_response :success
        assert(json_body.detect { |user| user['id'] == users(:mikolaj).id })
        assert_not(json_body.detect { |user| user['id'] == users(:wiktoria).id })
      end

      def test_index_with_system_filter
        user = users(:mikolaj)
        user.update_column(:system, true)

        get :index, params: { f: { system: true } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
        assert_equal user.id, json_body.first['id']
      end

      def test_index_with_remote_filter
        user = users(:mikolaj)
        user.update_column(:remote, true)

        get :index, params: { f: { remote: true } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
        assert_equal user.id, json_body.first['id']
      end

      def test_index_with_contract_of_employment_filter
        get :index, params: { f: { contract_of_employment: true } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
        assert_equal users(:milosz).id, json_body.first['id']
      end

      def test_index_with_b2b_filter
        get :index, params: { f: { b2b: true } }, format: :json

        assert_response :success
        assert_not(json_body.detect { |user| user['id'] == users(:milosz).id })
      end

      def test_index_for_project_managers
        global_role = global_roles(:global_pm)
        get :index, format: :json, params: { f: { project_managers: true } }
        assert_response :success
        assert_equal global_role.users.count, json_body.size
      end

      def test_index_includes_global_roles
        get :index, format: :json,
                    params: { f: { immediate_children: projects(:mkalita_project).id } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert_includes json_body.first, 'global_roles'
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index by state' do
        get :index, format: :json, params: { f: { state: 1 } }
        assert_response :success, @response.body.to_s
        if json_body.empty?
          skip
        else
          assert json_body.first['state'] == 'locked'
        end
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index by provenance: internal' do
        company_id = companies(:mkalita_company).id
        @user.update_column(:company_id, company_id)
        get :index, format: :json, params: { f: { provenance: '0', term: @user.username } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        json_body.each do |obj|
          assert_equal company_id, obj['company_id'] if obj['id'] == @user.id
        end
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index by provenance: external' do
        company_id = companies(:mkalita_company).id
        @user.update_column(:company_id, company_id)
        get :index, format: :json, params: { f: { provenance: '1', term: @user.username } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        json_body.each do |obj|
          assert_nil obj['company_id']
        end
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index by provenance: all' do
        company_id = companies(:mkalita_company).id
        @user.update_column(:company_id, company_id)
        get :index, format: :json, params: { f: { provenance: '', term: @user.username } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        json_body.each do |obj|
          assert_equal company_id, obj['company_id'] if obj['id'] == @user.id
        end
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index by provenance: (default)' do
        company_id = companies(:mkalita_company).id
        @user.update_column(:company_id, company_id)
        get :index, format: :json, params: { f: { term: @user.username } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        json_body.each do |obj|
          assert_equal company_id, obj['company_id'] if obj['id'] == @user.id
        end
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index paginated' do
        users_size = User.count
        get :index, format: :json, params: { per_page: 1 }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
        assert_equal "<http://test.host/api/users?page=#{users_size}&per_page=1>; rel=\"last\", <http://test.host/api/users?page=2&per_page=1>; rel=\"next\"", response.headers['Link']
      end

      test 'should get index page 2' do
        users_size = User.count
        user_on_first_page = User.order('id DESC').first!
        get :index, format: :json, params: { per_page: 1, page: 2 }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert json_body.last['id'].to_i > 0
        assert json_body.last['id'].to_i != user_on_first_page.id
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index sorted' do
        get :index, format: :json, params: { f: { sort: 'id asc' } }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert json_body.last['id'].to_i > json_body.first['id'].to_i
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index sorted by agreement_type asc' do
        get :index, format: :json, params: { f: { sort: 'agreement_type asc' }, per_page: 100 }
        assert_response :success, @response.body.to_s

        assert_equal json_body.first['contract_types'], []
        assert_equal json_body.last['contract_types'], ['UoP']
      end

      test 'should get index sorted by agreement_type desc' do
        get :index, format: :json, params: { f: { sort: 'agreement_type desc' }, per_page: 100 }
        assert_response :success, @response.body.to_s

        assert_equal json_body.first['contract_types'], ['UoP']
        assert_equal json_body.last['contract_types'], []
      end

      test 'should get index sorted by month_notice_period asc' do
        get :index, format: :json, params: { f: { sort: 'month_notice_period asc' }, per_page: 100 }
        assert_response :success, @response.body.to_s

        assert_equal json_body.first['contract_notice_period'], []
        assert_equal json_body.last['contract_notice_period'], [3]
      end

      test 'should get index sorted by month_notice_period desc' do
        get :index, format: :json, params: { f: { sort: 'month_notice_period desc' }, per_page: 100 }
        assert_response :success, @response.body.to_s

        assert_equal json_body.first['contract_notice_period'], [3]
        assert_equal json_body.last['contract_notice_period'], []
      end

      test 'should get index with filters' do
        get :index, format: :json, params: { f: { term: 'a' }, per_page: 1 }
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, 1
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'should get index with remote_allowed filter' do
        user = users(:milosz)

        get :index, format: :json, params: { f: { remote_allowed: true } }

        assert_response :success
        assert_equal json_body.size, 1
        assert_equal user.id, json_body.first['id']
      end

      test 'should get index with user_contract_type filter' do
        user = users(:milosz)

        get :index, format: :json, params: { f: { user_contract_type: 'b2b' } }

        assert_response :success
        assert json_body.all? {|user| user["contract_types"] == ["B2B"]}
      end

      test 'should get index' do
        get :index, format: :json
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, assigns(:users).size
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      def test_index_should_not_include_first_password
        get :index, format: :json
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert_nil json_body.last['first_password']
      end

      test 'index xlsx format' do
        get :index, format: :xlsx
        assert_response :success
      end

      test 'index as an unprivileged user' do
        user = users(:mikolaj)
        @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

        get :index, format: :json

        assert_response :success
        assert_equal 2, json_body.count
        assert(json_body.detect { |u| u['id'] == users(:wiktoria).id })
      end

      test 'should create user' do
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'John',
                                          last_name: 'Doe',
                                          email: '<EMAIL>',
                                          activates_on: Time.zone.tomorrow } }, format: :json
          assert_response 201, @response.body.to_s
        end
        assert_equal user_url(User.find(json_body['id'])), response.location
      end

      test 'should create user with complex group-projects-memberships' do
        assert_difference('User.count') do
          post :create, params: {
            user: {
              first_name: 'John',
              last_name: 'Doe',
              activates_on: Time.zone.tomorrow,
              company_id: companies(:one),
              group_ids: [groups(:two).id]
            }
          }, format: :json
        end

        assert_response :created
      end

      test 'should handle invalid user on creation' do
        assert_no_difference -> { User.count } do
          post :create, params: {
            user: {
              first_name: 'John',
              last_name: '',
              company_id: companies(:one)
            }
          }, format: :json
        end

        assert_response :unprocessable_entity
        assert_not_empty json_body['errors']['last_name']
      end

      test 'should allow setting :profile_comment on create' do
        comment = 'employee of the month!'

        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'John',
                                          last_name: 'Doe',
                                          email: '<EMAIL>',
                                          activates_on: Time.zone.tomorrow,
                                          profile_comment: comment } }, format: :json
          assert_response 201, @response.body.to_s
        end

        assert_equal comment, json_body['profile_comment']
      end

      test 'should generate username and email for user without company' do
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'John',
                                          last_name: 'Smith',
                                          email: '<EMAIL>',
                                          activates_on: Time.zone.tomorrow } }, format: :json
          assert_response 201, @response.body.to_s
          assert_equal '<EMAIL>', json_body['username']
          assert_equal '<EMAIL>', json_body['email']
        end
      end

      test 'should generate username and email with aliases for user with company' do
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'John',
                                          last_name: 'Smith',
                                          activates_on: Time.zone.tomorrow,
                                          company_id: companies(:two).id } }, format: :json
          user = User.last
          assert_response 201, @response.body.to_s
          assert_equal 'jsmith', json_body['username']
          assert_equal '<EMAIL>', json_body['email']
          assert_equal 5, user.email_aliases.count
        end
      end

      test 'should generate new username and email with aliases for updated user' do
        company = companies(:two)
        post :create, params: { user: { first_name: 'John', last_name: 'Smith', activates_on: Time.zone.tomorrow,
                                        company_id: company.id } }, format: :json
        user_id = json_body['id']
        put :update, params: { id: user_id, user: { last_name: 'Wayne' } }, format: :json
        assert_response 204
        user = User.find user_id
        assert_equal 'jwayne', user.username
        assert_equal 5, user.email_aliases.count
        email_alias = user.email_aliases.last
        assert email_alias.email.include?('wayne')
      end

      test 'should send email if set so' do
        assert_difference('ActionMailer::Base.deliveries.count', 1) do
          post :create, params: { user: { first_name: 'John',
                                          last_name: 'Smith',
                                          activates_on: Time.zone.tomorrow,
                                          company_id: companies(:two).id } }, format: :json
        end
      end

      test 'displays history properly' do
        user = users(:mkalita_user)
        authenticate(user)
        post :create, params: { user: { first_name: 'Johnatan', last_name: 'Doerty', activates_on: Time.zone.tomorrow,
                                        company_id: companies(:one).id } }, format: :json
        user_id = json_body['id']
        put :update, params: { id: user_id, user: { last_name: 'Smith',
                                                    global_role_ids:
                                                      [global_roles(:global_user).id] } },
                     format: :json
        get :history, params: { id: user_id }, format: :json
        assert_response :success
        assert_equal 2, json_body.count
        first_revision = json_body.first
        assert_equal(user.full_name, first_revision['author']['full_name'])
        refute_empty first_revision['changeset']
      end

      def test_create_response_should_include_first_password
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'Johnathan',
                                last_name: 'Doerty',
                                email: '<EMAIL>',
                                activates_on: Time.zone.tomorrow } }, format: :json
          assert_response 201, @response.body.to_s
        end
        created_record = User.find(json_body['id'])
        assert_equal json_body['first_password'], created_record.first_password, json_body.inspect
      end

      def test_create_username_from_name_with_dashes_and_non_ascii_letters
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'Łucja',
                                          last_name: 'Radziwił-Jasnożewska',
                                          email: '<EMAIL>',
                                          activates_on: Time.zone.tomorrow } }, format: :json
          assert_response 201, @response.body.to_s
        end
        created_record = User.find(json_body['id'])
        assert_equal '<EMAIL>', created_record.username
      end

      def test_create_department_ids
        department = Department.create!(
          name: 'Department Example',
          company_id: companies(:mkalita_company).id,
          chief_id: users(:mkalita_user).id,
          mpk_number: mpk_numbers(:other)
        )
        department_ids = [departments(:one).id, department.id]
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'Eduardo',
                                          last_name: 'Ackiski',
                                          email: '<EMAIL>',
                                          activates_on: Time.zone.tomorrow,
                                          department_ids: department_ids } }, format: :json
          assert_response 201, @response.body.to_s
        end
        created_record = User.find(json_body['id'])
        assert_equal user_url(created_record), response.location
        assert_equal created_record.department_ids, [departments(:one).id]
      end

      def test_create_global_role_ids
        global_role = GlobalRole.create(name: 'Global Example')
        global_role_ids = [global_roles(:global_user).id, global_role.id]
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'test_create_global_role_ids',
                                          last_name: 'test_create_global_role_ids',
                                          email: '<EMAIL>',
                                          activates_on: Time.zone.tomorrow,
                                          global_role_ids: global_role_ids } }, format: :json
          assert_response 201, @response.body.to_s
        end
        created_record = User.find(json_body['id'])
        assert_equal user_url(created_record), response.location
        assert_equal created_record.global_role_ids, global_role_ids
      end

      def test_create_group_ids
        group = Group.create(name: 'Group Example')
        group_ids = [groups(:mkalita_group).id, group.id]
        assert_difference('User.count') do
          post :create, params: { user: { first_name: 'Eduardo',
                                          last_name: 'Ackiski',
                                          email: '<EMAIL>',
                                          activates_on: Time.zone.tomorrow,
                                          group_ids: group_ids } }, format: :json
          assert_response 201, @response.body.to_s
        end
        created_record = User.find(json_body['id'])
        assert_equal user_url(created_record), response.location
        assert_equal created_record.group_ids, group_ids
      end

      test 'should show user' do
        get :show, params: { id: @user }, format: :json
        assert_response :success, @response.body.to_s
        assert_includes json_body, 'url'
        assert_includes json_body, 'system'
      end

      test 'show should contain cards information' do
        user = users(:wiktoria)

        get :show, params: { id: user }, format: :json

        assert_equal 1, json_body['cards_attributes'].count
      end

      def test_show_response_should_not_include_first_password
        get :show, params: { id: @user }, format: :json
        assert_response :success, @response.body.to_s
        assert_nil json_body['first_password']
      end

      def test_show_response_should_include_company_name
        @user.company = companies(:mkalita_company)
        @user.save
        get :show, params: { id: @user.id }, format: :json
        assert_response :success, @response.body.to_s
        assert_includes json_body, 'company_name'
      end

      def test_show_response_should_include_uid_number
        get :show, params: { id: @user.id }, format: :json
        assert_response :success, @response.body.to_s
        assert_includes json_body, 'uid_number'
      end

      # TODO: test actual scope
      def test_show_response_should_include_projects_scoped_for_current_user
        sign_in(@user)
        get :show, params: { id: @user }, format: :json
        assert_response :success, @response.body.to_s
        assert_includes json_body, 'projects'
      end

      test 'show response should include approvals for current user' do
        sign_in(@user)
        get :show, params: { id: @user }, format: :json
        assert_response :success, @response.body.to_s
        assert_includes json_body, 'approvals'
      end

      test 'should include docs_cloud value in response' do
        sign_in(@user)
        get :show, params: { id: @user }, format: :json
        assert_response :success, @response.body.to_s
        assert_equal false, json_body['docs_cloud']
      end

      test 'should update user' do
        put :update, params: { id: @user, user: { username: @user.username,
                                                  email: @user.email,
                                                  system: true } }, format: :json
        assert @user.reload.system
        assert_response 204, @response.body.to_s
      end

      test 'should create card on update' do
        user = users(:wiktoria)
        company = companies(:one)

        assert_difference -> { user.cards.count } do
          put :update, params: { id: user, user: {
            cards_attributes: [{ code: 2555, company_id: company, expires_on: 1.year.from_now,
                                 active: true }]
          } }, format: :json
        end
      end

      test 'should update :profile_comment' do
        comment = 'not the best worker'
        put :update, params: { id: @user, user: { username: @user.username,
                                                  email: @user.email,
                                                  profile_comment: comment } }, format: :json
        assert_equal comment, @user.reload.profile_comment
        assert_response 204, @response.body.to_s
      end

      test 'should generate new username and email when name changed' do
        post :create, params: { user: { first_name: 'John',
                                        last_name: 'Smith',
                                        email: '<EMAIL>',
                                        activates_on: Time.zone.tomorrow } }, format: :json
        assert_response 201, @response.body.to_s
        assert_equal '<EMAIL>', json_body['username']
        assert_equal '<EMAIL>', json_body['email']

        new_user = User.find(json_body['id'])

        put :update, params: { id: new_user.id, user: { first_name: 'Wesley' } }, format: :json
        assert_response 204, @response.body.to_s
        new_user.reload
        new_user.confirm

        put :update, params: { id: new_user.id, user: { last_name: 'Snipes'} }, format: :json
        assert_response 204, @response.body.to_s
        new_user.reload

        post :create, params: { user: { first_name: 'John',
                                        last_name: 'Smith',
                                        activates_on: Time.zone.tomorrow,
                                        company_id: companies(:two).id } }, format: :json
        assert_response 201, @response.body.to_s
        assert_equal 'jsmith', json_body['username']
        assert_equal '<EMAIL>', json_body['email']

        new_user2 = User.find(json_body['id'])

        put :update, params: { id: new_user2.id, user: { first_name: 'Wesley' } }, format: :json
        assert_response 204, @response.body.to_s
        new_user2.reload
        assert_equal 'wsmith', new_user2.username
        assert_equal '<EMAIL>', new_user2.email

        put :update, params: { id: new_user2.id, user: { last_name: 'Snipes' } }, format: :json
        assert_response 204, @response.body.to_s
        new_user2.reload
        assert_equal 'wsnipes', new_user2.username
        assert_equal '<EMAIL>', new_user2.email
      end

      test 'should generate new username and email when email changed for outside user' do
        post :create, params: { user: { first_name: 'John',
                                        last_name: 'Smith',
                                        email: '<EMAIL>',
                                        activates_on: Time.zone.tomorrow } }, format: :json
        assert_response 201, @response.body.to_s
        assert_equal '<EMAIL>', json_body['username']
        assert_equal '<EMAIL>', json_body['email']

        new_user = User.find(json_body['id'])
        put :update, params: { id: new_user.id, user: { email: '<EMAIL>'} }, format: :json
        assert_response 204, @response.body.to_s
        new_user.reload
        assert_equal '<EMAIL>', new_user.username
        assert_equal '<EMAIL>', new_user.email
      end

      def test_update_department_ids
        department = Department.create!(
          name: 'Other Department Example',
          company_id: companies(:mkalita_company).id,
          chief_id: users(:mkalita_user).id,
          mpk_number: mpk_numbers(:other)
        )
        department_ids = [department.id]
        put :update, params: { id: @user,
                               user: { department_ids: department_ids } },
                     format: :json
        assert_response 204, @response.body.to_s
        assert_equal @user.reload.department_ids, department_ids
      end

      def test_update_global_role_ids
        global_role = GlobalRole.create(name: 'Global Example')
        global_role_ids = [global_role.id]
        put :update, params: { id: @user,
                               user: { global_role_ids: global_role_ids } },
                     format: :json
        assert_response 204, @response.body.to_s
        assert_equal @user.reload.global_role_ids, global_role_ids
      end

      def test_update_group_ids
        group = Group.create(name: 'Group Example')
        group_ids = [group.id]
        put :update, params: { id: @user,
                               user: { group_ids: group_ids } },
                     format: :json
        assert_response 204, @response.body.to_s
        assert_equal @user.reload.group_ids, group_ids
      end

      test 'should change docs_cloud' do
        put :update, params: { id: @user, user: { docs_cloud: true } }, format: :json
        assert @user.reload.docs_cloud?
      end

      def test_resend_confirmation_404
        user = users(:mikolaj)
        assert_no_difference('ActionMailer::Base.deliveries.count') do
          post :resend_confirmation_instructions, params: { id: user.id }, format: :json
          assert_response :not_found
        end
      end

      def test_resend_confirmation
        user = users(:unconfirmed_user)
        assert_difference('ActionMailer::Base.deliveries.count') do
          post :resend_confirmation_instructions, params: { id: user.id }, format: :json
          assert_response :no_content
        end
        refute_empty user.reload.confirmation_token
      end

      test 'should destroy user' do
        user = users(:mrlocked)
        user.update_column(:confirmed_at, nil)
        assert_difference('User.count', -1) do
          delete :destroy, params: { id: user }, format: :json
          assert_response 204, @response.body.to_s
        end
      end

      def test_index_authorization
        viewer = users(:mkalita_user)
        policy = stub(index?: false)
        UserPolicy.stubs(:new).with(viewer, User).returns(policy)
        sign_in(viewer)
        get :index, format: :json
        assert_response 403, @response.body.to_s
      end

      def test_show_authorization
        viewer = users(:mkalita_user)
        user = users(:mrlocked)
        policy = stub(show?: false)
        UserPolicy.stubs(:new).with(viewer, user).returns(policy)
        sign_in(viewer)
        get :show, params: { id: user.id }, format: :json
        assert_response 403, @response.body.to_s
      end

      def test_create_authorization
        viewer = users(:mkalita_user)
        policy = stub(create?: false)
        UserPolicy.stubs(:new).with(viewer, User).returns(policy)
        sign_in(viewer)
        post :create, params: { user: { username: 'mrlocked',
                                        first_name: 'John',
                                        last_name: 'Locked',
                                        email: '<EMAIL>',
                                        password: "asdasdasd",
                                        password_confirmation: "asdasdasd",
                                        activates_on: Time.zone.tomorrow } }, format: :json
        assert_response 403, @response.body.to_s
      end

      def test_update_authorization
        viewer = users(:mkalita_user)
        user = users(:mrlocked)
        policy = stub(update?: false)
        UserPolicy.stubs(:new).with(viewer, user).returns(policy)
        sign_in(viewer)
        patch :update, params: { id: user.id, user: { username: 'unlocked'} }, format: :json
        assert_response 403, @response.body.to_s
      end

      def test_destroy_authorization
        viewer = users(:mkalita_user)
        user = users(:mrlocked)
        policy = stub(destroy?: false)
        UserPolicy.stubs(:new).with(viewer, user).returns(policy)
        delete :destroy, params: { id: user.id }, format: :json
        assert_response 403, @response.body.to_s
      end

      def test_impersonate
        user = users(:mkalita_user_alternative)
        user.global_roles << global_roles(:global_admin_programmer)
        @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

        other_user = users(:mkalita_user)

        make_impersonate_request(other_user)
        assert_response 200, @response.body.to_s
        refute json_body.empty?
        other_user.reload
        assert_equal other_user.tokens.first[0].to_s, json_body['data']['client']
        assert_equal other_user.tokens.first[1]['expiry'].to_s, json_body['data']['expiry']
        assert_equal other_user.email, json_body['data']['uid']
        assert_equal 'Bearer', json_body['data']['token-type']
        refute_empty json_body['data']['access-token']
      end

      def test_impersonate_without_http_basic_credentials_fails
        user = users(:mkalita_user_alternative)
        user.global_roles << global_roles(:global_admin_programmer)
        @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

        other_user = users(:mkalita_user)

        put :impersonate, params: { id: other_user }, format: :json
        assert_response 401, @response.body.to_s
        assert_equal "HTTP Basic: Access denied.\n", @response.body.to_s
      end

      def test_impersonate_for_not_global_admin_programmer
        user = users(:mkalita_user_alternative)
        user.global_roles.destroy_all
        user.global_roles << global_roles(:global_admin)
        @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

        other_user = users(:mkalita_user)

        make_impersonate_request(other_user)
        assert_response 403, @response.body.to_s
      end

      def test_impersonate_if_settings_impersonation_enabled_is_false
        Settings.impersonation_enabled = false
        user = users(:mkalita_user_alternative)
        user.global_roles << global_roles(:global_admin_programmer)
        @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

        other_user = users(:mkalita_user)

        make_impersonate_request(other_user)
        assert_response 422, @response.body.to_s
        assert_includes json_body, 'errors'
        assert_equal ['Impersonation disabled!'], json_body['errors']['base']
        Settings.impersonation_enabled = true
      end

      def test_impersonate_if_settings_impersonation_enabled_is_nil
        Settings.impersonation_enabled = nil
        user = users(:mkalita_user_alternative)
        user.global_roles << global_roles(:global_admin_programmer)
        @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

        other_user = users(:mkalita_user)

        make_impersonate_request(other_user)
        assert_response 422, @response.body.to_s
        assert_includes json_body, 'errors'
        assert_equal ['Impersonation disabled!'], json_body['errors']['base']
        Settings.impersonation_enabled = true
      end

      private

      def make_impersonate_request(other_user)
        with_http_login do
          put :impersonate, params: { id: other_user }, format: :json
        end
      end

      def http_login_start
        user = Settings.impersonation_login
        password = Settings.impersonation_password
        @request.env['HTTP_AUTHORIZATION'] =
          ActionController::HttpAuthentication::Basic.encode_credentials(user, password)
      end

      def http_login_cleanup
        @request.env['HTTP_AUTHORIZATION'] = nil
      end

      def with_http_login
        http_login_start
        yield
        http_login_cleanup
      end
    end
  end
end
