require 'test_helper'

module Api
  module V1
    module Accounting
      class PaymentsControllerTest < ActionController::TestCase
        setup do
          @user = users(:wik<PERSON>)
          authenticate(@user)
        end

        test 'index' do
          get :index, format: :json

          assert_response :success
          assert_equal 10, json_body.count
        end

        test 'index for responsible user' do
          user = users(:mikolaj)
          user.global_roles << global_roles(:global_pm)
          authenticate(user)

          get :index, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with project_id filter' do
          get :index, params: { f: { project_id: projects(:three).id } }, format: :json

          assert_response :success
          assert_equal 6, json_body.count
        end

        test 'index with company_id filter' do
          get :index, params: { f: { company_id: companies(:filmweb).id } }, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with client_id filter' do
          get :index, params: { f: { client_id: clients(:arte).id } }, format: :json

          assert_response :success
          assert_equal 10, json_body.count
        end
      end
    end
  end
end
