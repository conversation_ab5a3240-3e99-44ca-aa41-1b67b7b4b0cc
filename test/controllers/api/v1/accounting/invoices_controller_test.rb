require 'test_helper'

module Api
  module V1
    module Accounting
      class InvoicesControllerTest < ActionController::TestCase
        setup do
          @user = users(:wik<PERSON>)
          @invoice = invoices(:payment_ten_amendment)
          projects(:four).update(status: 0)
          authenticate(@user)
        end

        test 'index with state: :pending filter returns pending invoices' do
          get :index, params: { f: { state: 'pending' } }, format: :json

          assert_response :success
          assert_operator JSON.parse(@response.body).count, :>=, 1
          assert_equal Invoice.pending.order(invoice_date: :asc).page(1).per(10).decorate,
                       assigns(:invoices)
        end

        test 'index with state: :pending filter returns pending invoices of the specific scope' do
          @user.global_roles.destroy(global_roles(:global_accounting))

          get :index, params: { f: { state: 'pending' } }, format: :json

          assert_response :success
          assert_includes(assigns(:invoices), invoices(:project_five_payment_invoice).decorate)
        end

        test 'index as a responsible user' do
          authenticate(users(:mkalita_user))
          @user.memberships.create!(project: projects(:five), roles: [roles(:accounting_manager)])

          get :index, format: :json

          project_scope_ids = ProjectPolicy::Scope.new(@user, Project).resolve.pluck(:id)
          supervised_project_ids = Project.joins(memberships: :roles)
                                        .where(memberships: { member: @user }, roles: { responsible: true })
                                        .pluck(:id)

          invoices = Invoice.left_joins(payment: :payment_schedule)
                           .where(user_id: @user.id)
                           .or(Invoice.left_joins(payment: :payment_schedule)
                           .where(payment_schedules: { project_id: supervised_project_ids + project_scope_ids }))
                           .distinct

          assert_response :success
          assert_equal invoices.count, response.headers['X-Total'].to_i
        end

        test 'index with state: :accepted filter returns accepted invoices' do
          invoices(:project_five_payment_invoice).accept!(users(:wiktoria))

          get :index, params: { f: { state: 'accepted' } }, format: :json

          assert_response :success
          assert_operator JSON.parse(@response.body).count, :>=, 1
          assert_equal Invoice.accepted.order(invoice_date: :asc, number: :desc).page(1).per(10).decorate,
                       assigns(:invoices)
        end

        test 'index with state: :issued filter returns issued invoices' do
          get :index, params: { f: { state: 'issued' } }, format: :json

          assert_response :success
          assert_operator JSON.parse(@response.body).count, :>=, 1
          assert_equal Invoice.issued.order(invoice_date: :desc, number: :desc).page(1).per(10).decorate,
                       assigns(:invoices)
        end

        test 'index with project_id filters invoices by project' do
          project = projects(:two)

          get :index, params: { f: { project_id: project } }, format: :json

          assert_response :success
          assert_equal Invoice.joins(payment: :payment_schedule)
                              .where(payment_schedules: { project_id: project.id }).count,
                       assigns(:invoices).count
        end

        test 'index with company_id filters invoices by company' do
          company = companies(:two)

          get :index, params: { f: { company_id: company } }, format: :json

          assert_response :success
          assert_equal Invoice.joins(payment: { payment_schedule: :project })
                              .where(projects: { company_id: company.id }).count,
                       assigns(:invoices).count
        end

        test 'index with client_id filters invoices by client' do
          client = clients(:polexit)

          get :index, params: { f: { client_id: client } }, format: :json

          assert_response :success
          assert_equal Invoice.joins(payment: { payment_schedule: :project })
                              .where(projects: { client_id: client.id }).count,
                       assigns(:invoices).count
        end

        test 'index with date_from filters invoices by invoice date' do
          date_from = 1.day.ago

          get :index, params: { f: { date_from: date_from } }, format: :json

          assert_response :success
          assert_equal Invoice.where('invoices.invoice_date >= ?',
                                     date_from).count, assigns(:invoices).count
        end

        test 'index with date_to filters invoices by invoice date' do
          date_to = 1.week.ago

          get :index, params: { f: { date_to: date_to } }, format: :json

          assert_response :success
          assert_equal Invoice.where('invoices.invoice_date <= ?', date_to).count,
                       assigns(:invoices).count
        end

        test 'index with acceptor_id filter' do
          user = users(:milosz)
          user.global_roles << global_roles(:global_accounting)

          get :index, params: { f: { acceptor_id: user.id } }, format: :json

          assert_response :success
          assert_includes assigns(:invoices), @invoice
        end

        test 'index with user_id filter' do
          user = users(:mikolaj)

          get :index, params: { f: { user_id: user.id } }, format: :json

          invoices = Invoice.where(user_id: user.id)

          assert_response :success
          assert_equal invoices.count, assigns(:invoices).count
          assert_equal invoices.map(&:id).sort, assigns(:invoices).map(&:id).sort
        end

        test 'show returns invioce details' do
          get :show, params: { id: @invoice }, format: :json

          assert_response :success
          assert_equal @invoice, assigns(:invoice)
        end

        test 'show includes amended invoice positions attributes' do
          get :show, params: { id: @invoice }, format: :json
          assert_response :success

          amended_position = @invoice.amends.invoice_positions.first
          amended_position_attributes = json_body['invoice']['amended_invoice_positions_attributes'].first

          assert json_body['invoice']['any_invoice_positions_changes']
          assert_equal amended_position.amount.to_s, amended_position_attributes['amount']
          assert_equal amended_position.net_value.to_s, amended_position_attributes['net_value']
        end

        test 'accepted invoice should be transferred to pending by admin user' do
          board_user = users(:mkalita_global_admin_programmer)
          authenticate(board_user)
          @invoice.accept!(users(:wiktoria))
          @invoice.reload

          patch :back_to_pending, params: { id: @invoice }, format: :json

          assert_response :no_content
          assert @invoice.reload.pending?
        end

        test 'accepted invoice should not be transferred to pending by not admin user' do
          board_user = users(:board_member_user)
          authenticate(board_user)
          @invoice.accept!(users(:wiktoria))
          @invoice.reload

          patch :back_to_pending, params: { id: @invoice }, format: :json

          assert_response :forbidden
          assert @invoice.reload.accepted?
        end

        test 'accept sets pending invoice to accepted' do
          assert @invoice.pending?

          patch :accept, params: { id: @invoice }, format: :json

          assert_response :no_content
          assert @invoice.reload.accepted?
        end

        test 'issue sets accepted invoice to issued in case of paper invoice sending method' do
          @invoice.accept!(users(:wiktoria))
          @invoice.reload

          assert @invoice.issued_at.nil?
          assert_not @invoice.electronic_invoice_sending_method?

          patch :issue, params: { id: @invoice }, format: :json

          assert_response :no_content
          @invoice.reload
          assert @invoice.issued?
          assert @invoice.issued_at.present?
        end

        test 'issue fails for invoice which is not accepted' do
          assert @invoice.pending?

          patch :issue, params: { id: @invoice }, format: :json

          assert_response :not_found
          assert @invoice.reload.pending?
        end

        test 'issue sends to client in case of electronic invoice sending method' do
          @invoice.accept!(users(:wiktoria))
          @invoice.reload
          @invoice.client.update!(invoice_sending_method: :electronic, invoice_sending_email: '<EMAIL>')

          assert @invoice.electronic_invoice_sending_method?

          assert_difference('InvoiceSendingWorker.jobs.count', 1) do
            patch :issue, params: { id: @invoice }, format: :json
          end

          assert_response :no_content
          assert @invoice.reload.issued?
        end

        test 'reject sets invoice to rejected' do
          patch :reject, params: { id: @invoice }, format: :json

          assert_response :no_content
          assert @invoice.reload.rejected?
        end

        test 'update updates pending invoice' do
          today = Time.zone.today

          patch :update, params: { id: @invoice, invoice: { invoice_date: today } }, format: :json

          assert_response :no_content
          assert_equal today, @invoice.reload.invoice_date
        end

        test 'update accepted invoice' do
          @invoice.accept!(users(:wiktoria))

          today = Time.zone.today

          patch :update, params: { id: @invoice, invoice: { invoice_date: today } }, format: :json

          assert_response :no_content
          assert_equal today, @invoice.reload.invoice_date
        end

        test 'not admin user can not update accepted invoice' do
          board_user = users(:board_member_user)
          authenticate(board_user)
          @invoice.accept!(users(:wiktoria))

          today = Time.zone.today

          patch :update, params: { id: @invoice, invoice: { invoice_date: today } }, format: :json

          assert_includes json_body["errors"]["base"], "not allowed to update? this Invoice"
          assert_not_equal today, @invoice.reload.invoice_date
        end

        test 'update returns error on invalid attributes' do
          patch :update, params: { id: @invoice, invoice: { total_amount: 2000 } }, format: :json

          assert_response :unprocessable_entity
        end

        test 'edit' do
          get :edit, params: { id: @invoice }, format: :json

          assert_response :success
          assert_equal @invoice.decorate, assigns(:invoice)
        end

        test 'edit returns proper kinds for invoice kind payment' do
          get :edit, params: { id: @invoice }, format: :json

          assert_response :success
          assert_equal %w[vat re_invoice advance advance_accounting vat_barter], json_body['kinds'].pluck('key')
        end

        test 'edit returns proper kinds for accounting note kind payment' do
          @invoice.payment.update_columns(kind: :accounting_note)

          get :edit, params: { id: @invoice }, format: :json

          assert_response :success
          assert_equal ['accounting_note'], json_body['kinds'].pluck('key')
        end

        test 'invoice_document' do
          @invoice.invoice_document.document_data = TestData.document_data.to_json
          @invoice.invoice_document.save

          get :invoice_document, params: { id: @invoice }, format: :json

          assert_response :success
          assert_equal @response.headers['Content-Transfer-Encoding'], 'binary'
          assert_match "inline; filename=\"#{@invoice.invoice_document.document.original_filename}\"",
                       @response.headers['Content-Disposition']
        end

        test 'create_invoice_document' do
          invoice = invoices(:project_five_payment_invoice)
          invoice.accept!(users(:wiktoria))
          invoice.reload

          params = {
            invoice_document_attributes: {
              document: Rack::Test::UploadedFile.new('test/fixtures/files/sample.pdf', 'application/pdf')
            }
          }

          assert_difference('InvoiceDocument.count', 1) do
            post :create_invoice_document, params: { id: invoice, invoice: params }, format: :json
          end

          assert_response :created
          assert_equal 'sample.pdf', invoice.reload.invoice_document.document.original_filename
        end

        test 'history' do
          @invoice.accept!(users(:wiktoria))

          get :history, params: { id: @invoice }, format: :json

          assert_response :success
          assert_equal 1, json_body.count

          history_record = JSON.parse(response.body)['history_records'].first
          assert_equal 'accept', history_record['action']
          assert_equal 'pending', history_record['state_was']
          assert_equal @invoice.id, history_record['invoice']['id']
        end

        test 'monthly_report' do
          start_date = Time.zone.now.beginning_of_month.to_fs('YYYY-MM-DD')
          end_date = Time.zone.now.end_of_month.to_fs('YYYY-MM-DD')
          attribute_name = 'invoice_date'
          Payment.includes(:mpk_positions, :payment_schedule).find_each do |payment|
            payment.send :recalculate_scheduled_payments
          end

          get :monthly_report, params: { date_from: start_date, date_to: end_date,
                                         attribute_name: attribute_name }, format: :json

          assert_response :success
          assert response.body.to_s.match(clients(:arte).name)
        end

        test 'monthly report returns 400 in case of bad params' do
          get :monthly_report, format: :json

          assert_response :bad_request
        end

        test 'monthly report returns 400 in case of unparsable date' do
          get :monthly_report, params: { attribute_name: 'invoice_date',
                                         date_from: 'string', date_to: 'string' }, format: :json

          assert_response :bad_request
        end

        test 'transform_invoices_file processes valid XLSX file successfully' do
          # Create a simple temporary file to satisfy the file parameter requirement
          temp_file = Tempfile.new(['test_invoices', '.xlsx'])
          temp_file.write('dummy content')
          temp_file.rewind

          file = Rack::Test::UploadedFile.new(temp_file.path, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'test_invoices.xlsx')

          mock_data = [
            {
              client: 'Artegence',
              overdue_status: 'unpaid_invoices_to_five',
              company: 'Test Company',
              currency: 'PLN',
              amount: 1000.0,
              invoices_numbers: ['INV-001'],
              emails: '<EMAIL>',
              checked: true,
              content: '<p>Test email content</p>'
            }
          ]

          # Mock the UnpaidInvoicesXlsxReader constructor and read method
          mock_reader = mock('reader')
          mock_reader.stubs(:read).returns(mock_data)
          UnpaidInvoicesXlsxReader.stubs(:new).returns(mock_reader)

          post :transform_invoices_file, params: { document: file }, format: :json

          assert_response :ok

          # Convert expected data to string keys to match JSON response
          expected_data = mock_data.map { |item| item.transform_keys(&:to_s) }
          assert_equal expected_data, json_body['preview']

          temp_file.close
          temp_file.unlink
        end

        test 'transform_invoices_file returns error for invalid file format' do
          file = Rack::Test::UploadedFile.new('test/fixtures/files/sample.pdf', 'application/pdf')

          post :transform_invoices_file, params: { document: file }, format: :json

          assert_response :unprocessable_entity
          assert_equal 'Invalid file format. Please upload .xlsx', json_body['error']
        end

        test 'transform_invoices_file returns error when no file provided' do
          post :transform_invoices_file, format: :json

          assert_response :unprocessable_entity
          assert_equal 'Invalid file format. Please upload .xlsx', json_body['error']
        end

        test 'transform_invoices_file handles file processing errors' do
          temp_file = Tempfile.new(['test_invoices', '.xlsx'])
          temp_file.write('dummy content')
          temp_file.rewind

          file = Rack::Test::UploadedFile.new(temp_file.path, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'test_invoices.xlsx')

          # Mock the constructor to avoid file parsing, then mock read to raise error
          mock_reader = mock('reader')
          mock_reader.stubs(:read).raises(StandardError.new('File processing error'))
          UnpaidInvoicesXlsxReader.stubs(:new).returns(mock_reader)

          post :transform_invoices_file, params: { document: file }, format: :json

          assert_response :unprocessable_entity
          assert_includes json_body['error'], 'File processing error'

          temp_file.close
          temp_file.unlink
        end

        test 'transform_invoices_file handles headers not found error' do
          temp_file = Tempfile.new(['test_invoices', '.xlsx'])
          temp_file.write('dummy content')
          temp_file.rewind

          file = Rack::Test::UploadedFile.new(temp_file.path, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'test_invoices.xlsx')

          # Mock the constructor to raise the error during initialization
          UnpaidInvoicesXlsxReader.stubs(:new).raises(StandardError.new('Headers not found'))

          post :transform_invoices_file, params: { document: file }, format: :json

          assert_response :unprocessable_entity
          assert_includes json_body['error'], 'Headers not found'

          temp_file.close
          temp_file.unlink
        end

        test 'send_emails_to_clients sends emails successfully with valid data' do
          emails_data = [
            {
              client: 'Artegence',
              invoices_numbers: ['INV-001', 'INV-002'],
              emails: '<EMAIL>',
              overdue_status: 'unpaid_invoices_to_five',
              company: 'Test Company',
              content: '<p>Test email content</p>'
            },
            {
              client: 'Polexit',
              invoices_numbers: ['INV-003'],
              emails: '<EMAIL>',
              overdue_status: 'unpaid_invoices_six_to_thirty',
              company: 'Test Company',
              content: '<p>Another test email content</p>'
            }
          ]

          # Mock the mailer to avoid actually sending emails
          mock_mail = mock('mail')
          mock_mail.stubs(:deliver_later)
          InvoicesMailer.stubs(:unpaid_invoices_to_five).returns(mock_mail)
          InvoicesMailer.stubs(:unpaid_invoices_six_to_thirty).returns(mock_mail)

          post :send_emails_to_clients, params: { emails: emails_data }, format: :json

          assert_response :ok
          response_data = json_body['response']
          assert_equal 2, response_data.length

          # Check first email response
          assert_equal 'Artegence', response_data[0]['client']
          assert_equal ['INV-001', 'INV-002'], response_data[0]['invoices_numbers']
          assert_equal '<EMAIL>', response_data[0]['email']
          assert_equal 'Email successfully sent', response_data[0]['message']

          # Check second email response
          assert_equal 'Polexit', response_data[1]['client']
          assert_equal ['INV-003'], response_data[1]['invoices_numbers']
          assert_equal '<EMAIL>', response_data[1]['email']
          assert_equal 'Email successfully sent', response_data[1]['message']
        end

        test 'send_emails_to_clients returns error when no emails provided' do
          post :send_emails_to_clients, format: :json

          assert_response :unprocessable_entity
          assert_equal ['No emails provided'], json_body['errors']
        end

        test 'send_emails_to_clients returns error when emails data is blank' do
          post :send_emails_to_clients, params: { emails: [] }, format: :json

          assert_response :unprocessable_entity
          assert_equal ['No emails provided'], json_body['errors']
        end

        test 'send_emails_to_clients handles blank email addresses' do
          emails_data = [
            {
              client: 'Artegence',
              invoices_numbers: ['INV-001'],
              emails: '',
              overdue_status: 'unpaid_invoices_to_five',
              company: 'Test Company',
              content: '<p>Test email content</p>'
            }
          ]

          post :send_emails_to_clients, params: { emails: emails_data }, format: :json

          assert_response :ok
          response_data = json_body['response']
          assert_equal 1, response_data.length
          assert_equal 'Artegence', response_data[0]['client']
          assert_equal 'Email is blank', response_data[0]['error']
        end

        test 'send_emails_to_clients handles invalid email format' do
          emails_data = [
            {
              client: 'Artegence',
              invoices_numbers: ['INV-001'],
              emails: 'invalid-email-format',
              overdue_status: 'unpaid_invoices_to_five',
              company: 'Test Company',
              content: '<p>Test email content</p>'
            }
          ]

          post :send_emails_to_clients, params: { emails: emails_data }, format: :json

          assert_response :ok
          response_data = json_body['response']
          assert_equal 1, response_data.length
          assert_equal 'Artegence', response_data[0]['client']
          assert_equal 'Invalid email format', response_data[0]['error']
        end

        test 'send_emails_to_clients handles mixed valid and invalid emails' do
          emails_data = [
            {
              client: 'Artegence',
              invoices_numbers: ['INV-001'],
              emails: '<EMAIL>',
              overdue_status: 'unpaid_invoices_to_five',
              company: 'Test Company',
              content: '<p>Test email content</p>'
            },
            {
              client: 'Polexit',
              invoices_numbers: ['INV-002'],
              emails: 'invalid-email',
              overdue_status: 'unpaid_invoices_to_five',
              company: 'Test Company',
              content: '<p>Test email content</p>'
            }
          ]

          # Mock the mailer for valid email
          mock_mail = mock('mail')
          mock_mail.stubs(:deliver_later)
          InvoicesMailer.stubs(:unpaid_invoices_to_five).returns(mock_mail)

          post :send_emails_to_clients, params: { emails: emails_data }, format: :json

          assert_response :ok
          response_data = json_body['response']
          assert_equal 2, response_data.length

          # Check valid email response
          assert_equal 'Artegence', response_data[0]['client']
          assert_equal 'Email successfully sent', response_data[0]['message']

          # Check invalid email response
          assert_equal 'Polexit', response_data[1]['client']
          assert_equal 'Invalid email format', response_data[1]['error']
        end
      end
    end
  end
end
