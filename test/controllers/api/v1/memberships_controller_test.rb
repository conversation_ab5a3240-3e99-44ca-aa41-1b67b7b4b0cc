require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::MembershipsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :projects, :global_roles, :user_global_roles, :users, :groups, :roles

  teardown do
    Settings.default_management_role = 'Supervisor'
  end

  def membership
    @membership ||= memberships(:mkalita_membership_user)
    # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
    # HOWTO: test specific version
    # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
    # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
    # or
    # post '/humans',
    #      { human: { name: 'John', brain_type: 'small' } }.to_json,
    #      { 'Accept' => 'application/vnd.api+json; version=2',
    #        'Content-Type' => 'application/vnd.api+json; version=2' }
  end

  def test_exception_2
    get :show, params: { id: 345_345_345 }, format: :json
    assert_includes json_body, 'errors'
    assert_equal ["Couldn't find Membership with 'id'=345345345"], json_body['errors']['base']
  end

  def test_index
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:memberships).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_created_after_filter
    membership = memberships(:one)
    membership.update(created_at: 1.day.from_now)

    get :index, params: { f: { created_after: 1.day.from_now.to_date.to_s } }, format: :json

    assert_response :success
    assert_equal 1, json_body.size
    assert_equal membership.id, json_body.first['id'].to_i
  end

  def test_index_with_created_before_filter
    membership = memberships(:one)
    membership.update(created_at: 1.day.ago)

    get :index, params: { f: { created_before: 1.day.ago.to_date.to_s } }, format: :json

    assert_response :success
    assert_equal 1, json_body.size
    assert_equal membership.id, json_body.first['id'].to_i
  end

  def test_index_with_sort_by_clause
    get :index, params: { f: { sort: 'id ASC' } }, format: :json

    assert_response :success
    assert_equal(Membership.order('id ASC').limit(10).pluck(:id),
                 json_body.map { |membership| membership['id'].to_i })
  end

  def test_index_page_2
    user_on_first_page = Membership.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_scope_for_global_admin
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_admin)
    project = Project.create(name: 'test abc123',
                             identifier: 'test_index_scope_for_global_admin',
                             accounting_number: accounting_numbers(:zero),
                             company: companies(:one))
    assert project.persisted?

    other_user = users(:mkalita_user)
    role = roles(:pm)
    role_ids = [role.id]
    membership_params = { member_id: other_user.id, member_type: other_user.class.name, project_id: project.id, role_ids: role_ids }
    Membership.create!(membership_params)

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.each do |attributes|
      return true if attributes['id'] == project.id
    end

    get :index, format: :json,
                params: { f: { collection_for_select: true, project_id: project.id } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.each do |attributes|
      return true if attributes['id'] == project.id
    end
  end

  def test_index_scope_for_member_user
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_user)
    project = Project.create(name: 'test abc123',
                             identifier: 'test_index_scope_for_member_user',
                             accounting_number: accounting_numbers(:zero),
                             company: companies(:one))
    assert project.persisted?
    role = roles(:pm)
    role_ids = [role.id]
    membership_params = { member_id: user.id, member_type: user.class.name, project_id: project.id, role_ids: role_ids }
    Membership.create!(membership_params)

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    other_user = users(:mkalita_user)
    role = role = roles(:pm)
    role_ids = [role.id]
    membership_params = { member_id: other_user.id, member_type: other_user.class.name, project_id: project.id, role_ids: role_ids }
    Membership.create!(membership_params)

    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.each do |attributes|
      return true if attributes['id'] == project.id
    end

    get :index, format: :json,
                params: { f: { collection_for_select: true, project_id: project.id } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.each do |attributes|
      return true if attributes['id'] == project.id
    end
  end

  def test_index_scope_for_member_group
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_user)
    group = groups(:mkalita_group_alternative)
    user.group_ids = [group.id]
    project = Project.create(name: 'test abc123',
                             identifier: 'test_index_scope_for_member_group',
                             accounting_number: accounting_numbers(:zero),
                             company: companies(:one))
    assert project.persisted?
    role = roles(:pm)
    role_ids = [role.id]
    membership_params = { member_id: group.id, member_type: group.class.name, project_id: project.id, role_ids: role_ids }
    Membership.create!(membership_params)

    other_user = users(:mkalita_user)
    role = roles(:pm)
    role_ids = [role.id]
    membership_params = { member_id: other_user.id, member_type: other_user.class.name, project_id: project.id, role_ids: role_ids }
    Membership.create!(membership_params)

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.each do |attributes|
      return true if attributes['id'] == project.id
    end

    get :index, format: :json,
                params: { f: { collection_for_select: true, project_id: project.id } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.each do |attributes|
      return true if attributes['id'] == project.id
    end
  end

  def test_index_scope_for_non_member
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_user)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    project = Project.create(name: 'test abc123',
                             identifier: 'test_index_scope_for_member_group',
                             accounting_number: accounting_numbers(:zero),
                             company: companies(:one))
    assert project.persisted?

    other_user = users(:mkalita_user)
    role = roles(:pm)
    role_ids = [role.id]
    membership_params = { member_id: other_user.id, member_type: other_user.class.name, project_id: project.id, role_ids: role_ids }
    Membership.create!(membership_params)

    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert json_body.empty?

    get :index, format: :json, params: { f: { collection_for_select: true, project_id: project.id } }
    assert_response :success, @response.body.to_s
    assert_not_empty json_body
  end

  def test_create
    role_ids = [roles(:mkalita_role).id]
    assert_difference('Membership.count') do
      post :create, params: { membership: {
        project_id: projects(:mkalita_project).id,
        member_id: users(:mkalita_user_alternative).id,
        member_type: 'User',
        direct_role_ids: role_ids
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_includes json_body, 'url'
    assert_equal json_body['direct_role_ids'], role_ids
    assert_includes json_body, 'relationships'
    assert_equal membership_url(Membership.find(json_body['id'])), response.location
  end

  def test_create_multiple_for_user_ids
    role_ids = [roles(:mkalita_role).id]
    user_ids = [users(:mkalita_user_alternative).id]
    assert_difference('Membership.count') do
      post :create, params: { membership: {
        project_id: projects(:mkalita_project).id,
        direct_role_ids: role_ids,
        user_ids: user_ids
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_includes json_body, 'url'
    assert_equal json_body['direct_role_ids'], role_ids
    assert_includes json_body, 'relationships'
    assert_equal membership_url(Membership.find(json_body['id'])), response.location
  end

  def test_create_multiple_for_user_ids_with_rollback_for_role_ids
    user_ids = [users(:mkalita_user_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: [-1],
          user_ids: user_ids
        } }, format: :json
        assert_response 404, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    refute_empty json_body['errors']['base']
  end

  def test_create_multiple_for_user_ids_with_rollback_for_user_ids
    role_ids = [roles(:mkalita_role).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          role_ids: role_ids,
          user_ids: [-1]
        } }, format: :json
        assert_response 422, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    refute_empty json_body['errors']['member']
  end

  def test_create_multiple_for_user_ids_with_rollback_for_empty_role_ids
    user_ids = [users(:mkalita_user_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: [],
          user_ids: user_ids
        } }, format: :json
        assert_response 422, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    refute_empty json_body['errors']['direct_roles']
  end

  def test_create_multiple_for_group_ids
    role_ids = [roles(:mkalita_role).id]
    group_ids = [groups(:mkalita_group_alternative).id]
    assert_difference('Membership.count') do
      post :create, params: { membership: {
        project_id: projects(:mkalita_project).id,
        direct_role_ids: role_ids,
        group_ids: group_ids
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_includes json_body, 'url'
    assert_equal json_body['direct_role_ids'], role_ids
    assert_includes json_body, 'relationships'
    assert_equal membership_url(Membership.find(json_body['id'])), response.location
  end

  def test_create_multiple_for_group_ids_with_rollback_for_role_ids
    group_ids = [groups(:mkalita_group_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: [-1],
          group_ids: group_ids
        } }, format: :json
        assert_response 404, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    assert_not_empty json_body['errors']['base']
  end

  def test_create_multiple_for_group_ids_with_rollback_for_empty_role_ids
    group_ids = [groups(:mkalita_group_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          group_ids: group_ids
        } }, format: :json
        assert_response 422, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    assert_not_empty json_body['errors']['direct_roles']
  end

  def test_create_multiple_for_user_ids_and_group_ids
    role_ids = [roles(:mkalita_role).id]
    user_ids = [users(:mkalita_user_alternative).id]
    group_ids = [groups(:mkalita_group_alternative).id]
    assert_difference('Membership.count', +2) do
      assert_difference('MembershipRole.count', +2) do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: role_ids,
          user_ids: user_ids,
          group_ids: group_ids
        } }, format: :json
        assert_response 201, @response.body.to_s
      end
    end
    assert_includes json_body, 'url'
    assert_equal json_body['direct_role_ids'], role_ids
    assert_includes json_body, 'relationships'
    assert_equal membership_url(Membership.find(json_body['id'])), response.location
  end

  def test_create_multiple_for_user_ids_and_group_ids_with_rollback_for_user_ids
    role_ids = [roles(:mkalita_role).id]
    group_ids = [groups(:mkalita_group_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: role_ids,
          user_ids: [-1],
          group_ids: group_ids
        } }, format: :json
        assert_response 422, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    refute_empty json_body['errors']['member']
  end

  def test_create_multiple_for_user_ids_and_group_ids_with_rollback_for_group_ids
    role_ids = [roles(:mkalita_role).id]
    user_ids = [users(:mkalita_user_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: role_ids,
          user_ids: user_ids,
          group_ids: [-1]
        } }, format: :json
        assert_response 422, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    refute_empty json_body['errors']['member']
  end

  def test_create_multiple_for_user_ids_and_group_ids_with_rollback_for_role_ids
    user_ids = [users(:mkalita_user_alternative).id]
    group_ids = [groups(:mkalita_group_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: [-1],
          user_ids: user_ids,
          group_ids: group_ids
        } }, format: :json
        assert_response 404, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    refute_empty json_body['errors']['base']
  end

  def test_create_multiple_for_user_ids_and_group_ids_with_rollback_for_empty_role_ids
    user_ids = [users(:mkalita_user_alternative).id]
    group_ids = [groups(:mkalita_group_alternative).id]
    refute_difference('Membership.count') do
      refute_difference('MembershipRole.count') do
        post :create, params: { membership: {
          project_id: projects(:mkalita_project).id,
          direct_role_ids: [],
          user_ids: user_ids,
          group_ids: group_ids
        } }, format: :json
        assert_response 422, @response.body.to_s
      end
    end
    assert_includes json_body, 'errors'
    refute_empty json_body['errors']['direct_roles']
  end

  def test_show
    get :show, params: { id: membership }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
  end

  def test_update
    role_ids = [roles(:mkalita_role).id]
    put :update, params: { id: membership, membership: { role_ids: role_ids } },
                 format: :json
    assert_response 204, @response.body.to_s
    updated_membership = Membership.find(membership.id)
    assert_equal updated_membership.role_ids, role_ids
  end

  def test_update_role_ids
    role_ids = [roles(:mkalita_role).id]
    put :update, params: { id: membership, membership: {
      role_ids: role_ids
    } }, format: :json
    assert_response 204, @response.body.to_s
    updated_membership = Membership.find(membership.id)
    assert_equal updated_membership.role_ids, role_ids
  end

  def test_destroy
    assert_difference('Membership.count', -1) do
      delete :destroy, format: :json, params: { id: membership.id }
      assert_response 204, @response.body.to_s
    end
  end

  def test_destroy_with_inherited_roles
    membership_role = membership.membership_roles.first
    membership.membership_roles.create(role_id: membership_role.role_id,
                                       inherited_from: membership_role)
    assert_no_difference('Membership.count') do
      delete :destroy, format: :json, params: { id: membership }
    end

    assert_response 405
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    MembershipPolicy.stubs(:new).with(user, Membership).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    membership = memberships(:one)
    policy = stub(show?: false)
    MembershipPolicy.stubs(:new).with(user, membership).returns(policy)
    sign_in(user)
    get :show, params: { id: membership.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    policy = stub(manage_members?: false)
    project = projects(:mkalita_project)
    ProjectPolicy.stubs(:new).with(user, project).returns(policy)
    sign_in(user)
    post :create, params: { membership: {
      project_id: project.id, member_id: groups(:mkalita_group)
    } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    user = users(:mkalita_user)
    membership = memberships(:one)
    project = membership.project
    policy = stub(manage_members?: false)
    ProjectPolicy.stubs(:new).with(user, project).returns(policy)
    sign_in(user)
    patch :update, params: { id: membership.id, membership: { member_id: 4} }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_search_memberships_type_user
    get :index, format: :json,
                params: { f: { project_id: 'mystring', member_type: 'User', query: 'mgrabowski' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
  end

  def test_search_memberships_type_group
    get :index, format: :json,
                params: { f: { project_id: 'mystring', member_type: 'Group', query: 'MyString' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
  end

  def test_destroy_authorization
    user = users(:mkalita_user)
    membership = memberships(:one)
    project = membership.project
    policy = stub(manage_members?: false)
    ProjectPolicy.stubs(:new).with(user, project).returns(policy)
    sign_in(user)
    delete :destroy, params: { id: membership.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::MembershipsController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end
end
