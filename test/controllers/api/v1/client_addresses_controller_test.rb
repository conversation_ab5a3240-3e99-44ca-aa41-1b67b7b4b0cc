require 'test_helper'

module Api
  module V1
    class ClientAddressesControllerTest < ActionController::TestCase
      setup do
        user = users(:w<PERSON><PERSON>)
        authenticate(user)
      end

      test 'index works properly' do
        client = clients(:arte)

        get :index, params: { client_id: client }, format: :json

        assert_response :success
        assert_equal client_addresses(:arte).id, json_body.first['id']
      end

      test 'client address is created with valid params' do
        valid_attributes = {
          identifier: 'Artegence alternative',
          name: 'Artegence',
          street: 'Wołoska',
          street_number: '9a',
          city: 'Warszawa',
          post: 'Warszawa',
          voivodeship: 'mazowieckie',
          district: 'Warszawa',
          community: 'Mokotów',
          postcode: '00-999',
          country: 'Polska',
          vat_number: '1234PL',
          invoice_sending_method: :paper
        }

        assert_difference('ClientAddress.count') do
          post :create, params: { client_address: valid_attributes, client_id: clients(:arte) },
                        format: :json
        end

        assert_response :created
      end

      test 'validation fails properly on create' do
        post :create, params: { client_address: { vat_number: '' }, client_id: clients(:arte) },
                      format: :json

        assert_response :unprocessable_entity
        refute_empty json_body['errors']
      end

      test 'show works properly' do
        client_address = client_addresses(:arte)
        get :show, params: { client_id: clients(:arte), id: client_address }, format: :json

        assert_response :success
        assert_equal client_address.id, json_body['id']
      end

      test 'update works properly' do
        client_address = client_addresses(:arte)
        name = 'Artegence brigade'

        patch :update, params: { client_id: clients(:arte), id: client_address,
                                 client_address: { name: name } }, format: :json

        assert_response :no_content
        assert_equal name, client_address.reload.name
      end

      test 'update fails properly' do
        client_address = client_addresses(:arte)
        patch :update, params: { client_id: clients(:arte), id: client_address,
                                 client_address: { vat_number: '' } }, format: :json

        assert_response :unprocessable_entity
        refute_empty json_body['errors']
      end

      test 'destroy works properly' do
        client_address = client_addresses(:arte)
        assert_difference('ClientAddress.count', -1) do
          delete :destroy, params: { client_id: clients(:arte), id: client_address }, format: :json
        end
        assert_response :success
      end
    end
  end
end
