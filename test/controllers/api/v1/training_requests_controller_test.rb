require 'test_helper'

class Api::V1::TrainingRequestsControllerTest < ActionController::TestCase
  let(:valid_attributes) do
    {
      kind: :workshop, place: :office, mode: :stationary, starts_on: Date.current,
      ends_on: 1.month.from_now.to_date, provider: 'Provider', description: 'Description',
      tickets_price: 100, transportation_price: 100, accommodation_price: 100
    }
  end

  let(:training_request) { training_requests(:milosz_training_request) }

  test 'index' do
    authenticate users(:wiktoria)

    get :index, format: :json

    assert_response :success
    assert_equal training_request.id, json_body['training_requests'].first['id']
  end

  test 'index with sort' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { sort: 'kind asc' } }

    assert_response :success
    assert_equal training_requests(:wiktoria_training_request).id, json_body['training_requests'].first['id']
  end

  test 'index with the invalid sort' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { sort: 'invalid' } }

    assert_response :success
    assert_equal training_request.id, json_body['training_requests'].first['id']
  end

  test 'index with date_from filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { date_from: 1.month.from_now.to_date } }

    assert_response :success
    assert_equal 0, json_body['training_requests'].size
  end

  test 'index with date_to filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { date_to: 1.month.ago.to_date } }

    assert_response :success
    assert_equal 0, json_body['training_requests'].size
  end

  test 'index with user filter' do
    authenticate users(:wiktoria)
    user = users(:mikolaj)

    get :index, format: :json, params: { f: { user_id: user.id } }

    assert_equal 0, json_body['training_requests'].size
    user = users(:milosz)

    get :index, format: :json, params: { f: { user_id: user.id } }
    assert_equal 1, json_body['training_requests'].size
  end

  test 'index with pending_chief filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { state: %w[pending_chief] } }

    assert_equal 1, json_body['training_requests'].size

    training_requests(:wiktoria_training_request).accept!

    get :index, format: :json, params: { f: { state: %w[pending_chief] } }

    assert_equal 0, json_body['training_requests'].size
  end

  test 'index with pending_hr filter' do
    authenticate users(:wiktoria)
    training_request.update(state: :pending_chief)

    get :index, format: :json, params: { f: { state: %w[pending_hr] } }

    assert_equal 0, json_body['training_requests'].size

    training_request.accept!

    get :index, format: :json, params: { f: { state: %w[pending_hr] } }

    assert_equal 1, json_body['training_requests'].size
  end

  test 'index with accepted filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { state: %w[accepted] } }

    assert_equal 1, json_body['training_requests'].size # milosz_training_request
  end

  test 'index with rejected filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { state: %w[rejected] } }

    assert_equal 0, json_body['training_requests'].size

    training_request.reject!

    get :index, format: :json, params: { f: { state: %w[rejected] } }

    assert_equal 1, json_body['training_requests'].size
  end

  test 'index with departments_ids filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { departments_ids: [departments(:two)] } }

    assert_equal 1, json_body['training_requests'].size
    assert_equal 4000, json_body['training_stats']['yearly_limit'].to_d
  end

  test 'index as a regular user' do
    authenticate users(:milosz)

    get :index, format: :json

    assert_equal 1, json_body['training_requests'].size
  end

  test 'create' do
    user = users(:milosz)
    authenticate user

    assert_difference -> { user.training_requests.count } do
      post :create, format: :json, params: { training_request: valid_attributes }
    end

    assert_response :created
  end

  test 'create sends an e-mail' do
    user = users(:milosz)
    authenticate user

    assert_difference -> { TrainingRequestMailer.deliveries.count } do
      perform_enqueued_jobs do
        post :create, format: :json, params: { training_request: valid_attributes }
      end
    end

    email = TrainingRequestMailer.deliveries.last
    assert_equal I18n.t('mailers.training_request_mailer.training_request_created.subject', user_name: user.full_name),
                 email.subject
  end

  test 'create for another user' do
    user = users(:milosz)
    authenticate users(:wiktoria)

    assert_difference -> { user.training_requests.count } do
      post :create, format: :json, params: { training_request: valid_attributes.merge(user_id: user.id) }
    end

    assert_response :created
  end

  test 'create for another user as a regular user' do
    user = users(:mikolaj)
    author = users(:milosz)
    authenticate author

    assert_no_difference -> { user.training_requests.count } do
      assert_difference -> { author.training_requests.count } do
        post :create, format: :json, params: { training_request: valid_attributes.merge(user_id: user.id) }
      end
    end
  end

  test 'new for low-privileged' do
    authenticate users(:milosz)

    get :new, format: :json

    assert_response :success
    assert_not json_body['global_create']
  end

  test 'new for high-privileged' do
    authenticate users(:wiktoria)

    get :new, format: :json

    assert_response :success
    assert json_body['global_create']
  end

  test 'show' do
    authenticate users(:wiktoria)

    get :show, format: :json, params: { id: training_request }

    assert_response :success
    assert_equal training_request.id, json_body['id']
  end

  test 'update' do
    authenticate users(:wiktoria)
    new_ends_on = 2.months.from_now.to_date

    patch :update, format: :json, params: { id: training_request, training_request: { ends_on: new_ends_on } }

    assert_response :no_content
    assert_equal new_ends_on, training_request.reload.ends_on
  end

  test 'update for unprivileged user' do
    new_ends_on = 2.months.from_now.to_date
    authenticate training_request.user

    patch :update, format: :json, params: { id: training_request, training_request: { ends_on: new_ends_on } }

    assert_response :forbidden
  end

  test 'destroy' do
    authenticate users(:wiktoria)

    assert_difference -> { TrainingRequest.count }, -1 do
      delete :destroy, format: :json, params: { id: training_request }
    end

    assert_response :no_content
  end

  test 'destroy for unprivileged user' do
    authenticate training_request.user

    assert_no_difference -> { TrainingRequest.count } do
      delete :destroy, format: :json, params: { id: training_request }
    end

    assert_response :forbidden
  end

  test 'accept' do
    authenticate users(:wiktoria)
    training_request.update!(state: :pending_chief)

    patch :accept, params: { id: training_request }, format: :json

    assert_response :no_content
    assert training_request.reload.pending_hr?
  end

  test 'accept sends an e-mail' do
    authenticate users(:wiktoria)
    training_request.update!(state: :pending_hr)

    assert_difference -> { TrainingRequestMailer.deliveries.count } do
      perform_enqueued_jobs do
        patch :accept, params: { id: training_request }, format: :json
      end
    end

    email = TrainingRequestMailer.deliveries.last
    assert_equal I18n.t('mailers.training_request_mailer.training_request_accepted.subject'), email.subject
  end

  test 'accept for hr acceptation' do
    authenticate users(:wiktoria)
    training_request.update!(state: :pending_hr)

    patch :accept, params: { id: training_request }, format: :json

    assert_response :no_content
    assert training_request.reload.accepted?
  end

  test 'forced acceptation' do
    authenticate users(:wiktoria)
    training_request.update!(state: :pending_hr)
    training_request.update!(tickets_price: 100_000)

    patch :accept, params: { id: training_request, force: true }, format: :json

    assert_response :no_content
    assert training_request.reload.accepted?
  end

  test 'failed acceptation' do
    authenticate users(:wiktoria)
    training_request.update!(state: :pending_hr, tickets_price: 100_000, force: true)

    patch :accept, params: { id: training_request }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['tickets_price']
  end

  test 'reject' do
    authenticate users(:wiktoria)
    training_request.update!(state: :pending_chief)

    patch :reject, params: { id: training_request }, format: :json

    assert_response :no_content
    assert training_request.reload.rejected?
  end

  test 'reject sends an e-mail' do
    authenticate users(:wiktoria)
    training_request.update!(state: :pending_hr)

    assert_difference -> { TrainingRequestMailer.deliveries.count } do
      perform_enqueued_jobs do
        patch :reject, params: { id: training_request }, format: :json
      end
    end

    email = TrainingRequestMailer.deliveries.last
    assert_equal I18n.t('mailers.training_request_mailer.training_request_rejected.subject'), email.subject
  end
end
