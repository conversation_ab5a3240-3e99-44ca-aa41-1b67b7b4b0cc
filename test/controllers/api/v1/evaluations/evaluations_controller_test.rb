require 'test_helper'

module Api
  module V1
    module Evaluations
      class EvaluationsControllerTest < ActionController::TestCase
        setup do
          user = users(:wik<PERSON>)
          authenticate(user)
        end

        let(:subject) { users(:milosz) }

        let(:subject_evaluation) { evaluations(:milosz_pending_evaluation) }

        let(:valid_params) do
          { main_survey_id: surveys(:onboarding).id, starts_on: Date.today,
            name: 'First evaluation' }
        end

        test 'new returns user name' do
          get :new, params: { user_id: subject.id }, format: :json
          assert_response :success
          assert_equal subject.full_name, json_body['user']['full_name']
        end

        test 'new returns surveys list' do
          get :new, params: { user_id: subject.id }, format: :json
          assert_response :success
          assert_equal Survey.count, json_body['surveys'].count
        end

        test 'create passes with valid params' do
          assert_difference('Evaluation.count') do
            post :create, params: { user_id: subject.id, evaluation: valid_params }, format: :json
          end
          assert_response :created
        end

        test 'create rejects with invalid params' do
          assert_no_difference('Evaluation.count') do
            post :create, params: { user_id: subject.id, evaluation: { name: '' } }, format: :json
          end
          assert_response :unprocessable_entity
        end

        test 'edit passes with valid params' do
          new_name = 'new evaluation name'
          patch :update, params: { user_id: subject.id, id: subject_evaluation.id,
                                   evaluation: { name: new_name } },
                         format: :json
          assert_response :success
          assert_equal new_name, subject_evaluation.reload.name
        end

        test 'edit fails with invalid params' do
          patch :update, params: { user_id: subject.id, id: subject_evaluation.id,
                                   evaluation: { name: '' } },
                         format: :json
          assert_response :unprocessable_entity
          refute_empty json_body['errors']['name']
        end

        test 'shows works and returns correct data' do
          get :show, params: { user_id: subject.id, id: subject_evaluation.id }, format: :json
          assert_response :success
          assert_equal subject_evaluation.starts_on, Date.parse(json_body['starts_on'])
        end

        test 'start evaluation works properly' do
          patch :start, params: { user_id: subject.id, id: subject_evaluation.id }, format: :json
          assert_response :no_content
          assert_equal Date.today, subject_evaluation.reload.starts_on
        end

        test 'attempt to start already started evaluation returns 404' do
          evaluation = evaluations(:milosz_evaluation)
          patch :start, params: { user_id: subject.id, id: evaluation.id }, format: :json
          assert_response :not_found
        end

        test 'stop evaluation works properly' do
          evaluation = evaluations(:milosz_evaluation)
          patch :stop, params: { user_id: subject.id, id: evaluation.id,
                                 evaluation: { comment: '' } },
                       format: :json
          assert_response :no_content
          assert_equal Date.today, evaluation.reload.ends_on
        end

        test 'attempt to stop pending evaluation returns 404' do
          patch :stop, params: { user_id: subject.id, id: subject_evaluation.id,
                                 evaluation: { comment: '' } },
                       format: :json

          assert_response :not_found
        end

        test 'discard evaluation works properly' do
          evaluation = evaluations(:milosz_pending_evaluation)
          patch :discard, params: { user_id: subject.id, id: evaluation.id }
          assert_response :no_content
          assert_equal 'discarded', evaluation.reload.state
        end
      end
    end
  end
end
