require 'test_helper'

module Api
  module V1
    module Evaluations
      class UsersControllerTest < ::ActionController::TestCase
        setup do
          user = users(:w<PERSON><PERSON>)
          authenticate(user)
        end

        let(:subject) { users(:milosz) }

        test 'index returns all subordinates' do
          get :index, format: :json
          assert_response :success
          assert_equal departments(:two).users.native.count, json_body.count
        end

        test 'index returns starts_on and end_on' do
          last_evaluation = evaluations(:milosz_evaluation)
          next_evaluation = evaluations(:milosz_pending_evaluation)
          get :index, format: :json
          milosz_row = json_body.detect { |u| u['full_name'] == users(:milosz).full_name }
          assert_equal last_evaluation.starts_on, Date.parse(milosz_row['last_evaluation_on'])
          assert_equal next_evaluation.starts_on, Date.parse(milosz_row['next_evaluation_on'])
        end

        test 'show returns user data' do
          get :show, params: { id: subject }, format: :json
          assert_response :success
          assert_equal subject.full_name, json_body['full_name']
          assert_equal subject.evaluations.count, json_body['evaluations'].count
        end
      end
    end
  end
end
