require 'test_helper'

module Api
  module V1
    module Evaluations
      class SurveyAnswersControllerTest < ActionController::TestCase
        setup do
          @user = users(:w<PERSON><PERSON>)
          authenticate(@user)
        end

        let(:survey_answer) { survey_answers(:onboarding_chief_answer) }

        test 'index returns all surveys to fill' do
          get :index, format: :json
          assert_response :success
          assert_equal @user.survey_answers.pending.count, json_body.count
        end

        test 'show returns survey with questions' do
          get :show, params: { id: survey_answer.id }, format: :json
          assert_response :success
          assert_equal survey_answer.question_answers.count, json_body['question_answers'].count
        end

        test 'update works properly' do
          question_answer = survey_answer.question_answers.first
          answer = 'Example answer'
          params = {
            state: 'completed', question_answers_attributes: [{
              id: question_answer.id, content: answer, numeric_content: 1
            }]
          }
          patch :update, params: { id: survey_answer.id, survey_answer: params }, format: :json
          assert_response :no_content
          assert_equal 'completed', survey_answer.reload.state
          assert_equal answer, question_answer.reload.content
        end

        test 'update shows errors' do
          question_answer = survey_answer.question_answers.first
          answer = ''
          params = {
            state: 'completed', question_answers_attributes: [{
              id: question_answer.id, content: answer
            }]
          }
          patch :update, params: { id: survey_answer.id, survey_answer: params }, format: :json
          assert_response :unprocessable_entity
        end
      end
    end
  end
end
