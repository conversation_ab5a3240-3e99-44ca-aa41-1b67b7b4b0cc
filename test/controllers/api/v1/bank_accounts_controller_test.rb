require 'test_helper'

class Api::V1::BankAccountsControllerTest < ActionController::TestCase
  let(:bank_account) { bank_accounts(:arte_account) }
  let(:valid_attributes) do
    {
      company_id: companies(:one).id,
      name: 'Bank account',
      account_number: 'PL61 1090 1014 0000 0712 1981 2874',
      bank_name: '<PERSON><PERSON>'
    }
  end

  setup do
    @user = users(:wik<PERSON>)
    authenticate(@user)
  end

  test 'index' do
    get :index, format: :json

    assert_response :success
    assert_equal BankAccount.count, assigns(:bank_accounts).count
  end

  test 'index with company_id param' do
    company = companies(:one)

    get :index, format: :json, params: { f: { company_id: company } }

    assert_response :success
    assert_equal company.bank_accounts.count, assigns(:bank_accounts).count
  end

  test 'show' do
    get :show, params: { id: bank_account }, format: :json

    assert_response :success
    assert_equal bank_account, assigns(:bank_account)
  end

  test 'create' do
    assert_difference('BankAccount.count') do
      post :create, params: { bank_account: valid_attributes }, format: :json
    end

    assert_response :created
  end

  test 'update' do
    patch :update, params: { id: bank_account, bank_account: valid_attributes }, format: :json

    assert_equal valid_attributes[:name], bank_account.reload.name
    assert_response :no_content
  end
end
