require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::GroupsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :global_roles, :user_global_roles, :users

  def group
    @group ||= groups(:mkalita_group)
    # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
    # HOWTO: test specific version
    # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
    # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
    # or
    # post '/humans',
    #      { human: { name: 'John', brain_type: 'small' } }.to_json,
    #      { 'Accept' => 'application/vnd.api+json; version=2',
    #        'Content-Type' => 'application/vnd.api+json; version=2' }
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::GroupsController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  test 'should get index with param collection_for_select and page paginated' do
    groups_size = Group.includes(:active_users).count
    get :index, format: :json, params: { per_page: 1, f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
    assert_equal "<http://test.host/api/groups?f%5Bcollection_for_select%5D=true&page=#{groups_size}&per_page=1>; rel=\"last\", <http://test.host/api/groups?f%5Bcollection_for_select%5D=true&page=2&per_page=1>; rel=\"next\"", response.headers['Link']
  end

  test 'should get index with param collection_for_select and page paginated page 2' do
    groups_size = Group.includes(:active_users).count
    group_on_first_page = Group.includes(:active_users).order(id: :desc).first!
    get :index, format: :json, params: { per_page: 1, page: 2, f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != group_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'should get index and show only active users in groups' do
    active_users_being_in_a_group_count = User.where(state: 'active').map(&:groups).count { |groups| groups.present? }
    get :index, format: :json
    assert_response :success, @response.body.to_s
    users_in_response_count = json_body.map { |group| group['users'] }.flatten.map { |user| user['id'] }.uniq.count
    assert_equal users_in_response_count, active_users_being_in_a_group_count
  end

  def test_index_with_param_collection_for_select
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:groups).size
    assert !json_body.first.include?('created_at')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'test_index_with_param_not_member_of_project' do
    get :index, format: :json, params: { f: { collection_for_select: true, not_member_of_project: group.memberships.first.project.id } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, Group.all.size - 1
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'index with search term' do
    get :index, params: { f: { term: 'MyString' } }, format: :json

    assert_response :success
    assert_equal 2, json_body.size
  end

  test 'index with created_after filter' do
    groups(:one).update_column(:created_at, 2.months.ago)

    get :index, params: { f: { created_after: 1.month.ago.to_date } }, format: :json

    assert_response :success
    assert_equal Group.count - 1, json_body.size
  end

  test 'index with created_before filter' do
    groups(:one).update_column(:created_at, 2.months.ago)

    get :index, params: { f: { created_before: 1.month.ago.to_date } }, format: :json

    assert_response :success
    assert_equal 1, json_body.size
  end

  test 'index sorted by id' do
    get :index, params: { f: { sort: 'id desc' } }, format: :json

    assert_response :success
    assert_equal Group.order(id: :desc).ids,
                 (json_body.map { |group_entry| group_entry['id'].to_i })
  end

  test 'index works with unprivileged user' do
    user = users(:mikolaj)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    get :index, format: :json

    assert_response :success
    assert_equal user.groups.count, json_body.size
  end

  def test_index
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:groups).size
    assert_includes json_body.first, 'users'
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_page_2
    user_on_first_page = Group.includes(:active_users).order(id: :desc).first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_create
    assert_difference('Group.count') do
      post :create, params: { group: {
        name: group.name + '_test_create'
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_includes json_body, 'url'
    assert_equal group_url(Group.find(json_body['id'])), response.location
  end

  def test_create_user_ids
    user_ids = [users(:mkalita_user).id]
    assert_difference('Group.count') do
      post :create, params: { group: {
        name: group.name + '_test_create',
        user_ids: user_ids
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_includes json_body, 'url'
    created_record = Group.find(json_body['id'])
    assert_equal group_url(created_record), response.location
    assert_equal created_record.user_ids, user_ids
  end

  def test_show
    get :show, params: { id: group }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
  end

  # TODO: test actual scope
  def test_show_response_should_include_projects_scoped_for_current_user
    sign_in(users(:mkalita_user))
    get :show, params: { id: group }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'projects'
  end

  def test_update
    put :update, params: { id: group, group: {
      name: group.name
    } }, format: :json
    assert_response 204, @response.body.to_s
  end

  def test_update_as_hr_user
    user = users(:hr_user)
    @request.headers['X-Swagger-Sign-In-As'] = user
    user_ids = [users(:mkalita_user).id]
    name = 'name2'

    put :update, params: { id: group, group: { name:, user_ids: } }, format: :json

    assert_not_equal name, group.reload.name
    assert_equal user_ids, group.user_ids
  end

  def test_update_user_ids
    user_ids = [users(:mkalita_user).id]
    put :update, params: { id: group, group: {
      name: group.name,
      user_ids: user_ids
    } }, format: :json
    assert_response 204, @response.body.to_s
    assert_equal @group.reload.user_ids, user_ids
  end

  def test_destroy
    assert_difference('Group.count', -1) do
      delete :destroy, format: :json, params: { id: group }
      assert_response 204, @response.body.to_s
    end
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    GroupPolicy.stubs(:new).with(user, Group).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    group = groups(:mkalita_group)
    policy = stub(show?: false)
    GroupPolicy.stubs(:new).with(user, group).returns(policy)
    sign_in(user)
    get :show, params: { id: group.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    policy = stub(create?: false)
    GroupPolicy.stubs(:new).with(user, Group).returns(policy)
    sign_in(user)
    post :create, params: { group: { name: 'MyString' } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    user = users(:mkalita_user)
    group = groups(:mkalita_group)
    policy = stub(update?: false)
    GroupPolicy.stubs(:new).with(user, group).returns(policy)
    sign_in(user)
    patch :update, params: { id: group.id, group: { name: 'NewString' } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_destroy_authorization
    user = users(:mkalita_user)
    group = groups(:mkalita_group)
    policy = stub(destroy?: false)
    GroupPolicy.stubs(:new).with(user, group).returns(policy)
    sign_in(user)
    delete :destroy, params: { id: group.id }, format: :json
    assert_response 403, @response.body.to_s
  end
end
