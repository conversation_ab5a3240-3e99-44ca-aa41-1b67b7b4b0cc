require 'test_helper'

module Api
  module V1
    class ViolationRegistersControllerTest < ActionController::TestCase
      let(:violation_register) { violation_registers(:one) }
      let(:admin_user) { users(:mkalita_user) }
      let(:valid_params) { {
        description: 'Test violation description',
        violation_type: 'Safety violation',
        violation_area: 'Office area',
        taken_actions: 'Corrective measures implemented',
        violation_level: 'administrator',
        signature: 'Test Signature',
        company_id: companies(:one).id,
        violation_occurrence_date: Date.current,
        violation_entry_date: Date.current,
        mandatory_report: false
      } }

      setup do
        authenticate(admin_user)
      end

      test 'admin can access index' do
        get :index, format: :json
        assert_response :success
        assert_not_empty json_body
      end

      test 'index supports filtering by term' do
        get :index, params: { f: { term: 'Safety' } }, format: :json
        assert_response :success

        assert_equal 1, json_body.size
        assert_includes json_body.first['description'], 'Safety'
      end

      test 'index supports filtering by company_id' do
        get :index, params: { f: { company_id: companies(:one).id } }, format: :json
        assert_response :success

        json_body.each do |vr|
          assert_equal companies(:one).name, vr['company']
        end
      end

      test 'index supports pagination' do
        get :index, params: { per_page: 1 }, format: :json
        assert_response :success
        assert_equal 1, json_body.size
      end

      test 'index supports pagination - page and per_page' do
        get :index, params: { per_page: 3, page: 2 }, format: :json
        assert_response :success
        assert_equal 1, json_body.size
      end

      test 'admin can show violation register' do
        get :show, params: { id: violation_register.id }, format: :json
        assert_response :success

        violation_data = json_body['violation_register']
        assert_equal violation_register.id, violation_data['id']
        assert_equal violation_register.description, violation_data['description']
        assert_equal violation_register.violation_type, violation_data['violation_type']
        assert_equal violation_register.created_by.full_name, violation_data['creator']
      end

      test 'show returns 404 for non-existent violation register' do
        get :show, params: { id: 99999 }, format: :json
        assert_response :not_found
        assert_equal ['Violation register not found'], json_body['errors']
      end

      test 'admin can create violation register' do
        assert_difference('ViolationRegister.count', 1) do
          post :create, params: { violation_register: valid_params }, format: :json
        end

        assert_response :success
        created_violation = ViolationRegister.last
        assert_equal admin_user, created_violation.created_by
        assert_equal valid_params[:description], created_violation.description
      end

      test 'create with mandatory report requires report description' do
        params = valid_params.merge(mandatory_report: true)

        post :create, params: { violation_register: params }, format: :json
        assert_response :unprocessable_entity
        assert_includes json_body['errors']['report_description'], "can't be blank"
      end

      test 'create with mandatory report and description succeeds' do
        params = valid_params.merge(
          mandatory_report: true,
          report_description: 'Detailed violation report'
        )

        assert_difference('ViolationRegister.count', 1) do
          post :create, params: { violation_register: params }, format: :json
        end

        assert_response :success
        created_violation = ViolationRegister.last
        assert created_violation.mandatory_report?
        assert_equal 'Detailed violation report', created_violation.report_description
      end

      test 'create with invalid params returns validation errors' do
        invalid_params = valid_params.merge(description: '', violation_type: '')

        post :create, params: { violation_register: invalid_params }, format: :json
        assert_response :unprocessable_entity
        assert_includes json_body['errors']['description'], "can't be blank"
        assert_includes json_body['errors']['violation_type'], "can't be blank"
      end

      test 'admin can update violation register' do
        new_description = 'Updated violation description'

        patch :update, params: {
          id: violation_register.id,
          violation_register: { description: new_description }
        }, format: :json

        assert_response :success
        assert_equal new_description, violation_register.reload.description
      end

      test 'update with invalid params returns validation errors' do
        patch :update, params: {
          id: violation_register.id,
          violation_register: { description: '' }
        }, format: :json

        assert_response :unprocessable_entity
        assert_includes json_body['errors']['description'], "can't be blank"
      end

      test 'admin can destroy violation register' do
        assert_difference('ViolationRegister.count', -1) do
          delete :destroy, params: { id: violation_register.id }, format: :json
        end

        assert_response :no_content
      end

      test 'processing action transitions from registered to processing' do
        violation_register = violation_registers(:one)
        assert violation_register.registered?

        patch :processing, params: { id: violation_register.id }, format: :json
        assert_response :no_content
        assert violation_register.reload.processing?
      end

      test 'processing action fails with invalid transition' do
        violation_register = violation_registers(:two)
        assert violation_register.processing?

        patch :processing, params: { id: violation_register.id }, format: :json
        assert_response :unprocessable_entity
        assert_includes json_body['errors'].first, 'State transition error'
      end

      test 'reported action transitions from processing to reported' do
        violation_register = violation_registers(:two)
        assert violation_register.processing?

        patch :reported, params: { id: violation_register.id }, format: :json
        assert_response :no_content
        assert violation_register.reload.reported?
      end

      test 'complete action transitions from reported to completed' do
        violation_register = violation_registers(:three)
        assert violation_register.reported?

        patch :complete, params: { id: violation_register.id }, format: :json
        assert_response :no_content
        assert violation_register.reload.completed?
      end

      test 'attachment action returns 404 when no attachment' do
        get :attachment, params: { id: violation_register.id }, format: :json
        assert_response :not_found
      end

      test 'actions return 404 for non-existent violation register' do
        non_existent_id = 99999

        patch :update, params: {
          id: non_existent_id,
          violation_register: { description: 'test' }
        }, format: :json
        assert_response :not_found

        delete :destroy, params: { id: non_existent_id }, format: :json
        assert_response :not_found

        patch :processing, params: { id: non_existent_id }, format: :json
        assert_response :not_found
      end
    end
  end
end
