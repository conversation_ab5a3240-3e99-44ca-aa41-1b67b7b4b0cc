require "test_helper"

class Api::V1::RiskAnalysesControllerTest < ActionController::TestCase
  let(:admin_user) { users(:mkalita_user) }
  let(:regular_user) { users(:milosz) }
  let(:risk_analysis) { risk_analyses(:one) }
  let(:valid_params) do
    {
      risk_analysis: {
        name: "Test Risk Analysis",
        company_id: companies(:one).id,
        registry_activity_id: registry_activities(:one).id,
        creation_date: Date.today.to_s
      }
    }
  end
  let(:invalid_params) do
    {
      risk_analysis: {
        name: nil,
        company_id: companies(:one).id,
        registry_activity_id: registry_activities(:one).id,
        creation_date: Date.today.to_s
      }
    }
  end
  let(:update_params) { { id: risk_analysis.id, risk_analysis: { name: "Updated Risk Analysis" } } }
  let(:invalid_update_params) { { id: risk_analysis.id, risk_analysis: { name: nil } } }
  let(:risk_analysis_item) { risk_analysis_items(:marketing_confidentiality) }
  let(:valid_item_params) do
    {
      id: risk_analysis.id,
      risk_analysis_item: {
        property: "confidentiality",
        danger: "Test danger",
        vulnerability_description: "Test vulnerability",
        security: "Test security measures",
        probability: "possible",
        effect: "high",
        data_processing_impact_assessment: "required"
      }
    }
  end
  let(:invalid_item_params) do
    {
      id: risk_analysis.id,
      risk_analysis_item: {
        property: nil,
        danger: nil,
        vulnerability_description: nil,
        security: nil,
        probability: nil,
        effect: nil,
        data_processing_impact_assessment: nil
      }
    }
  end
  let(:update_item_params) { { id: risk_analysis.id, item_id: risk_analysis_item.id, risk_analysis_item: { danger: "Updated danger description" } } }
  let(:invalid_update_item_params) { { id: risk_analysis.id, item_id: risk_analysis_item.id, risk_analysis_item: { danger: nil } } }

  setup do
    authenticate(admin_user)
  end

  test "returns risk analyses - index" do
    get :index, params: {}, format: :json

    assert_response :success
    assert_equal RiskAnalysis.count, json_body.size
  end

  test "returns forbidden for regular user - index" do
    authenticate(regular_user)
    get :index, format: :json

    assert_response :forbidden
  end

  test "returns risk analysis - show" do
    get :show, params: { id: risk_analysis.id }, format: :json

    assert_response :success
    assert_equal risk_analysis.id, json_body["risk_analysis"]["id"]
  end

  test "creates risk analysis - create" do
    assert_difference('RiskAnalysis.count', 1) do
      post :create, params: valid_params, format: :json
    end

    assert_response :success
    assert_equal valid_params[:risk_analysis][:name], json_body['name']
    assert_equal valid_params[:risk_analysis][:company_id], json_body['company_id']
    assert_equal valid_params[:risk_analysis][:registry_activity_id], json_body['registry_activity_id']
    assert_equal valid_params[:risk_analysis][:creation_date], json_body['creation_date']
  end

  test "updates risk analysis - update" do
    patch :update, params: update_params, format: :json

    assert_response :success
    assert_equal update_params[:risk_analysis][:name], risk_analysis.reload.name
  end

  test "destroys risk analysis - destroy" do
    assert_difference('RiskAnalysis.count', -1) do
      delete :destroy, params: { id: risk_analysis.id }, format: :json
    end

    assert_response :no_content
  end

  test "returns error for create with invalid parameters" do
    assert_no_difference('RiskAnalysis.count') do
      post :create, params: invalid_params, format: :json
    end

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "name"
  end

  test "returns error for update with invalid parameters" do
    patch :update, params: invalid_update_params, format: :json

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "name"
  end

  test "returns not found for non-existent record - show" do
    get :show, params: { id: 999999 }, format: :json

    assert_response :not_found
    assert_not_empty json_body["errors"]
  end

  test "filters risk analyses by term" do
    get :index, params: { f: { term: 'Marketing' } }, format: :json

    assert_response :success
    assert json_body.all? { |ra| ra['name'].include?('Marketing') }
  end

  test "filters risk analyses by company_id" do
    company_id = companies(:one).id
    get :index, params: { f: { company_id: company_id } }, format: :json

    assert_response :success
    assert RiskAnalysis.where(id: json_body.map { |el| el['id'] }).all? { |ra| ra.company_id == company_id }
  end

  test "filters risk analyses by state" do
    get :index, params: { f: { state: 'active' } }, format: :json

    assert_response :success
    assert RiskAnalysis.includes(:registry_activity).where(id: json_body.map { |el| el['id'] }).all? { |ra| ra.registry_activity.state == 'active' }
  end

  test "paginates results" do
    get :index, params: { page: 2, per_page: 3 }, format: :json

    assert_response :success
    assert_equal json_body.size, 2
  end

  test "creates risk analysis item - create_risk_analysis_item" do
    assert_difference('RiskAnalysisItem.count', 1) do
      post :create_risk_analysis_item, params: valid_item_params, format: :json
    end

    assert_response :no_content
    created_item = risk_analysis.risk_analysis_items.last
    assert_equal valid_item_params[:risk_analysis_item][:danger], created_item.danger
    assert_equal valid_item_params[:risk_analysis_item][:vulnerability_description], created_item.vulnerability_description
    assert_equal valid_item_params[:risk_analysis_item][:security], created_item.security
    assert_equal "confidentiality", created_item.property
    assert_equal "possible", created_item.probability
    assert_equal "high", created_item.effect
    assert_equal "required", created_item.data_processing_impact_assessment
  end

  test "returns error for create_risk_analysis_item with invalid parameters" do
    assert_no_difference('RiskAnalysisItem.count') do
      post :create_risk_analysis_item, params: invalid_item_params, format: :json
    end

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "property"
    assert_includes json_body["errors"].keys, "danger"
    assert_includes json_body["errors"].keys, "vulnerability_description"
    assert_includes json_body["errors"].keys, "security"
    assert_includes json_body["errors"].keys, "probability"
    assert_includes json_body["errors"].keys, "effect"
    assert_includes json_body["errors"].keys, "data_processing_impact_assessment"
  end

  test "returns not found for non-existent risk analysis - create_risk_analysis_item" do
    invalid_params = valid_item_params.merge(id: 999999)
    post :create_risk_analysis_item, params: invalid_params, format: :json

    assert_response :not_found
    assert_not_empty json_body["errors"]
  end

  test "updates risk analysis item - update_risk_analysis_item" do
    original_danger = risk_analysis_item.danger
    patch :update_risk_analysis_item, params: update_item_params, format: :json

    assert_response :no_content
    assert_equal update_item_params[:risk_analysis_item][:danger], risk_analysis_item.reload.danger
    assert_not_equal original_danger, risk_analysis_item.danger
  end

  test "returns error for update_risk_analysis_item with invalid parameters" do
    patch :update_risk_analysis_item, params: invalid_update_item_params, format: :json

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "danger"
  end

  test "returns not found for non-existent risk analysis - update_risk_analysis_item" do
    invalid_params = update_item_params.merge(id: 999999)
    patch :update_risk_analysis_item, params: invalid_params, format: :json

    assert_response :not_found
    assert_not_empty json_body["errors"]
  end

  test "destroys risk analysis item - destroy_risk_analysis_item" do
    item_to_destroy = risk_analysis.risk_analysis_items.first
    assert_difference('RiskAnalysisItem.count', -1) do
      delete :destroy_risk_analysis_item, params: { id: risk_analysis.id, item_id: item_to_destroy.id }, format: :json
    end

    assert_response :no_content
  end

  test "returns not found for non-existent risk analysis - destroy_risk_analysis_item" do
    item_to_destroy = risk_analysis.risk_analysis_items.first
    delete :destroy_risk_analysis_item, params: { id: 999999, item_id: item_to_destroy.id }, format: :json

    assert_response :not_found
    assert_not_empty json_body["errors"]
  end

  test "returns not found for non-existent risk analysis item - destroy_risk_analysis_item" do
    delete :destroy_risk_analysis_item, params: { id: risk_analysis.id, item_id: 999999 }, format: :json

    assert_response :not_found
  end
end
