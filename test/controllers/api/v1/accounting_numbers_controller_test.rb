require 'test_helper'

module Api
  module V1
    class AccountingNumbersControllerTest < ActionController::TestCase
      let(:accounting_number) { accounting_numbers(:one) }
      let(:valid_attributes) do
        {
          company_id: companies(:one).id,
          description: 'Accounting number description',
          user_id: users(:wiktoria).id,
          overhead: true
        }
      end

      setup do
        @user = users(:wiktoria)
        authenticate(@user)
      end

      test 'index' do
        get :index, format: :json

        assert_response :success
        assert_equal AccountingNumber.count, assigns(:accounting_numbers).size
      end

      test 'index with not_locked filter' do
        get :index, params: { f: { not_locked: true } }, format: :json

        assert_response :success
        assert_equal AccountingNumber.where(locked: false).count, assigns(:accounting_numbers).size
      end

      test 'index with locked filter' do
        get :index, params: { f: { locked: true } }, format: :json

        assert_response :success
        assert_equal AccountingNumber.where(locked: true).count, assigns(:accounting_numbers).size
      end

      test 'index with overhead filter' do
        get :index, params: { f: { overhead: true } }, format: :json

        assert_response :success
        assert_equal AccountingNumber.where(overhead: true).count, assigns(:accounting_numbers).size
      end

      test 'index with company filter' do
        company = companies(:one)
        get :index, params: { f: { company_id: company } }, format: :json

        assert_response :success
        assert_equal AccountingNumber.where(company:).count, assigns(:accounting_numbers).size
      end

      test 'index with user filter' do
        user = users(:wiktoria)

        get :index, params: { f: { user_id: user } }, format: :json

        assert_response :success
        assert_equal AccountingNumber.where(user: ).count, assigns(:accounting_numbers).size
      end

      test 'index with not_locked filter set to false' do
        get :index, params: { f: { not_locked: false } }, format: :json

        assert_response :success
        assert_equal AccountingNumber.where(locked: true).count, assigns(:accounting_numbers).size
      end

      test 'index with bu filter' do
        get :index, params: { f: { bu: 'tech' } }, format: :json

        assert_response :success
        assert_equal [accounting_numbers(:two)], assigns(:accounting_numbers)
      end

      test 'index returns active_projects_count' do
        get :index, format: :json

        assert_response :success
        assert_equal accounting_number.projects.active.count, json_body.detect { |number|
          number['id'] == accounting_number.id
        }['active_projects_count']
      end

      test 'index with bu (g&a) filter' do
        ga_accounting_number = accounting_numbers(:three)
        get :index, params: { f: { bu: 'g&a' } }, format: :json

        assert_response :success
        assert_equal [ga_accounting_number], assigns(:accounting_numbers)
      end

      test 'show' do
        get :show, params: { id: accounting_number }, format: :json

        assert_response :success
        assert_equal accounting_number, assigns(:accounting_number)
      end

      test 'create' do
        assert_difference('AccountingNumber.count') do
          post :create, params: { accounting_number: valid_attributes }, format: :json
        end

        assert_response :created
      end

      test 'update' do
        patch :update, params: { id: accounting_number, accounting_number: valid_attributes },
                       format: :json

        assert_equal valid_attributes[:description], accounting_number.reload.description
        assert_response :no_content
      end

      test 'destroy accounting number with projects' do
        assert_no_difference('AccountingNumber.count') do
          delete :destroy, params: { id: accounting_number }, format: :json
        end

        assert_response :unprocessable_entity
        assert_equal ["You can't delete accounting number with assigned projects."], json_body['errors']['projects']
      end

      test 'destroy accounting number with no project' do
        accounting_number = AccountingNumber.create(valid_attributes)
        assert_difference('AccountingNumber.count', -1) do
          delete :destroy, params: { id: accounting_number }, format: :json
        end

        assert_response :no_content
      end
    end
  end
end
