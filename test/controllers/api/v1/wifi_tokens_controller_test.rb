require 'test_helper'

class Api::V1::WifiTokensControllerTest < ActionController::TestCase
  setup do
    @mock = mock('wifi_client')
    @user = users(:mkalita_global_admin_programmer)
    authenticate(@user)
    @valid_params = { purpose: 'foo', hours_valid: 2 }
    WifiToken.any_instance.stubs(:client).returns(@mock)
  end

  test 'create with valid params' do
    @mock.expects(:call).returns({ ok: true }.with_indifferent_access)
    assert_difference('WifiToken.count') do
      post :create, params: { wifi_token: @valid_params }, format: :json
    end
    %w(token purpose expires_at).each do |attr|
      assert_not_nil json_body[attr]
    end
  end

  test 'try to create with invalid params' do
    post :create, params: { wifi_token: { purpose: 'foo' } }, format: :json
    assert_response :unprocessable_entity
  end

  test 'wifi tokens index' do
    get :index, format: :json
    assert_response :success
    assert_equal @user.wifi_tokens.count, json_body.count
    assert_not_nil json_body.first['expires_at']
    refute json_body.first['expired']
    assert json_body.last['expired']
  end

  test 'index with only active' do
    get :index, params: { f: { active: true } }, format: :json
    assert_response :success
    assert_equal 1, json_body.count
    refute json_body.first['expired']
  end

  test 'index with only inactive' do
    get :index, params: { f: { active: false } }, format: :json
    assert_response :success
    assert_equal 1, json_body.count
    assert json_body.first['expired']
  end
end
