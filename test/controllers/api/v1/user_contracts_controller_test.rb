require 'test_helper'

class Api::V1::UserContractsControllerTest < ActionController::TestCase
  let(:user) { users(:wiktoria) }
  let(:user_contract) { user_contracts(:wiktoria_contract) }
  let(:valid_attributes) do
    {
      agreement_type: 'employment',
      starts_on: 1.day.ago.to_date,
      month_notice_period: 3
    }
  end

  setup do
    authenticate(user)
  end

  test 'index returns all user\'s contracts' do
    get :index, params: { user_id: user }, format: :json

    assert_response :success
    assert_equal user_contract.id, json_body.first['id']
  end

  test 'show returns specific user\'s contract' do
    get :show, params: { user_id: user, id: user_contract }, format: :json

    assert_response :success
    assert_equal user_contract.id, json_body['id']
  end

  test 'create adds new record' do
    assert_difference -> { user.user_contracts.reload.count } do
      post :create, params: { user_id: user, user_contract: valid_attributes }, format: :json
    end

    assert_response :created
  end

  test 'create returns 422 in case of validation error' do
    assert_no_difference -> { user.user_contracts.reload.count } do
      post :create, params: { user_id: user,
                              user_contract: valid_attributes.merge(starts_on: nil) },
                    format: :json
    end

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['starts_on']
  end

  test 'update modifies a record' do
    date = Time.zone.today
    patch :update, params: { user_id: user, id: user_contract,
                             user_contract: { starts_on: date, ends_on: date } }, format: :json

    assert_equal date, user_contract.reload.starts_on
    assert_response :no_content
  end

  test 'update returns 422 in case of validation error' do
    patch :update, params: { user_id: user, id: user_contract,
                             user_contract: { starts_on: nil } }, format: :json

    assert_not_nil user_contract.reload.starts_on
    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['starts_on']
  end

  test 'destroy removes a record' do
    assert_difference -> { user.user_contracts.reload.count }, -1 do
      delete :destroy, params: { user_id: user, id: user_contract }, format: :json
    end

    assert_response :success
  end
end
