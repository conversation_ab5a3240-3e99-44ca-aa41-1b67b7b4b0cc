require 'test_helper'

module Api
  module V1
    class RegistryCategoriesControllerTest < ActionController::TestCase
      let(:registry_category) { registry_categories(:one) }
      let(:active_registry) { registry_categories(:one) }
      let(:closed_registry) { registry_categories(:closed) }

      let(:global_admin) do
        user = users(:mkalita_user)
        user.global_roles << global_roles(:global_admin) unless user.global_roles.include?(global_roles(:global_admin))
        user
      end

      let(:user_with_registry_permissions) do
        user = users(:mikolaj)
        user.global_roles << global_roles(:global_admin) unless user.global_roles.include?(global_roles(:global_admin))
        user
      end

      let(:regular_user) do
        user = users(:milosz)
        user.global_roles << global_roles(:global_user) unless user.global_roles.include?(global_roles(:global_user))
        user
      end
      let(:valid_attributes) {
        {
          project_id: projects(:one).id,
          entrustment_agreement: 'Test entrustment agreement',
          processing_categories: 'Test processing categories',
          security_measures_description: 'Test security measures description',
          admin_name: 'Test admin name',
          admin_contact_details: 'Test admin contact details',
          processing_time: 'Test processing time',
          third_country_or_intl_org_recipients: 'Test third country recipients',
          creation_date: Date.current.to_s
        }
      }

      setup do
        authenticate(global_admin)
      end

      test 'index returns all registry categories' do
        get :index, format: :json
        assert_response :success
        assert_equal json_body.size, RegistryCategory.all.count
      end

      test 'index returns all registry categories for user with registry permissions' do
        authenticate(user_with_registry_permissions)

        get :index, format: :json
        assert_response :success
        assert_equal json_body.size, RegistryCategory.all.count
      end

      test 'index returns none registry categories for regular user' do
        authenticate(regular_user)

        get :index, format: :json
        assert_response :forbidden
        assert_not_empty json_body['errors']
      end

      test 'index with pagination returns correct number of records' do
        get :index, params: { per_page: 2 }, format: :json
        assert_response :success
        assert_equal 2, json_body.size
      end

      test 'index with filtering by state returns filtered records' do
        get :index, params: { f: { state: 'closed' } }, format: :json
        assert_response :success

        json_body.each do |record|
          registry_category = RegistryCategory.find(record['id'])
          assert registry_category.closed?
        end
      end

      test 'index with filtering by term returns filtered records' do
        get :index, params: { f: { term: 'Expired' } }, format: :json
        assert_response :success

        json_body.each do |record|
          assert_includes record['admin_name'], 'Expired'
        end
      end

      test 'index with filtering by company_id returns filtered records' do
        company_id = registry_categories(:project_two_registry).project.company_id

        get :index, params: { f: { company_id: company_id } }, format: :json
        assert_response :success

        json_body.each do |record|
          registry_category = RegistryCategory.find(record['id'])
          assert_equal company_id, registry_category.project.company_id
        end
      end

      test 'index with sorting by admin_name returns sorted records' do
        get :index, params: { f: { sort: 'admin_name asc' } }, format: :json
        assert_response :success

        admin_names = json_body.map { |record| record['admin_name'] }
        assert_equal admin_names.sort, admin_names
      end

      test 'index with sorting by creation_date returns sorted records' do
        get :index, params: { f: { sort: 'creation_date desc' } }, format: :json
        assert_response :success

        creation_dates = json_body.map { |record| record['creation_date'] }
        assert_equal creation_dates.sort.reverse, creation_dates
      end

      test 'show returns registry category details' do
        get :show, params: { id: registry_category.id }, format: :json
        assert_response :success

        assert_equal registry_category.id, json_body['registry_category']['id']
        assert_equal registry_category.admin_name, json_body['registry_category']['admin_name']
        assert_equal registry_category.project_id, json_body['registry_category']['project_id']
      end

      test 'show returns 404 for non-existent registry category' do
        get :show, params: { id: 999999 }, format: :json
        assert_response :not_found
        assert_equal ['Registry category not found'], json_body['errors']
      end

      test 'create with valid attributes creates registry category' do
        assert_difference('RegistryCategory.count') do
          post :create, params: { registry_category: valid_attributes }, format: :json
        end

        assert_response :created

        registry = RegistryCategory.find(json_body['id'])
        assert_equal valid_attributes[:admin_name], registry.admin_name
        assert_equal users(:mkalita_user).id, registry.created_by_id
      end

      test 'create with invalid attributes returns errors' do
        invalid_attributes = valid_attributes.merge(admin_name: '')

        assert_no_difference('RegistryCategory.count') do
          post :create, params: { registry_category: invalid_attributes }, format: :json
        end

        assert_response :unprocessable_entity
        assert_includes json_body['errors'].keys, 'admin_name'
      end

      test 'update with valid attributes updates registry category' do
        new_admin_name = 'Updated Admin Name'

        put :update, params: {
          id: registry_category.id,
          registry_category: { admin_name: new_admin_name }
        }, format: :json

        assert_response :success
        registry_category.reload
        assert_equal new_admin_name, registry_category.admin_name
      end

      test 'update with invalid attributes returns errors' do
        put :update, params: {
          id: registry_category.id,
          registry_category: { admin_name: ' ' }
        }, format: :json

        assert_response :unprocessable_entity
        assert_includes json_body['errors'].keys, 'admin_name'
      end

      test 'destroy removes registry category' do
        assert_difference('RegistryCategory.count', -1) do
          delete :destroy, params: { id: registry_category.id }, format: :json
        end

        assert_response :no_content
      end

      test 'activate transitions registry category from closed to active' do
        assert closed_registry.closed?

        post :activate, params: { id: closed_registry.id }, format: :json

        assert_response :no_content
        closed_registry.reload
        assert closed_registry.active?
      end

      test 'activate returns error for invalid transition' do
        assert active_registry.active?

        post :activate, params: { id: active_registry.id }, format: :json

        assert_response :unprocessable_entity
        assert_includes json_body['errors'].first, 'State transition error'
      end

      test 'close transitions registry category from active to closed' do
        assert active_registry.active?

        post :close, params: { id: active_registry.id }, format: :json

        assert_response :no_content
        active_registry.reload
        assert active_registry.closed?
      end

      test 'close returns error for invalid transition' do
        assert closed_registry.closed?

        post :close, params: { id: closed_registry.id }, format: :json

        assert_response :unprocessable_entity
        assert_includes json_body['errors'].first, 'State transition error'
      end
    end
  end
end
