require 'test_helper'

class Api::V1::CalendarMonthsControllerTest < ActionController::TestCase
  describe Api::V1::CalendarMonthsController do
    before do
      authenticate(users(:wiktoria))
      users(:wilhelm).update!(company: companies(:one))
    end

    describe 'index' do
      it 'succeeds with no filters other than date and with fill_default option' do
        get :index, as: :json, params: { f: { year: 2023, month: 12 }, fill_default: true }

        assert_response :success
        assert_equal  User.active.native.where.not(system: true).count, json_body['calendar_months'].count
      end

      it 'succeeds for department chief' do
        authenticate(users(:mikolaj))

        get :index, as: :json, params: { f: { year: 2024, month: 1 }, fill_default: true }

        assert_response :success
        assert_equal 1, json_body['calendar_months'].count
        assert_equal calendar_months(:wilhelm_2024_1).id, json_body['calendar_months'].first['id']
      end

      it 'filters by user_id' do
        get :index, params: { f: { user_id: users(:milosz).id }, year: 2023, month: 12 }, as: :json

        assert_response :success
        assert_equal 1, json_body['calendar_months'].count
        assert_equal calendar_months(:milosz_2023_12).id, json_body['calendar_months'].first['id']
      end

      it 'filters by user_redmine_id' do
        user = users(:milosz)
        user.update_columns(redmine: true, redmine_id: 1234)

        get :index, params: { f: { user_redmine_id: user.redmine_id } }, as: :json

        assert_response :success
        assert_equal 1, json_body['calendar_months'].count
        assert_equal calendar_months(:milosz_2023_12).id, json_body['calendar_months'].first['id']
      end

      it 'filters by departments_ids' do
        get :index, params: { f: { departments_ids: [users(:wilhelm).department_id], year: 2024, month: 1 },
                              fill_default: true }, as: :json

        assert_response :success
        assert_equal 1, json_body['calendar_months'].count
        assert_equal calendar_months(:wilhelm_2024_1).id, json_body['calendar_months'].first['id']
      end

      it 'filters existing calendar months by departments_ids' do
        get :index, params: { f: { departments_ids: [users(:wilhelm).department_id], year: 2023, month: 12 },
                              fill_default: true }, as: :json

        assert_response :success
        assert_equal 1, json_body['calendar_months'].count
      end

      it 'filters by project_id' do
        get :index, params: { f: { project_id: projects(:one).id, year: 2024, month: 1 }, fill_default: true },
                    as: :json

        assert_response :success
        assert_equal 3, json_body['calendar_months'].count
        assert json_body['calendar_months'].all? { |cm| cm['id'].nil? }
      end

      it 'filters by year' do
        get :index, params: { f: { year: 2023 } }, as: :json

        assert_response :success
        assert_equal 1, json_body['calendar_months'].count
        assert_equal calendar_months(:milosz_2023_12).id, json_body['calendar_months'].first['id']
      end

      it 'filters by month' do
        get :index, params: { f: { year: 2024, month: 1 } }, as: :json

        assert_response :success
        assert_equal 1, json_body['calendar_months'].count
        assert_equal calendar_months(:wilhelm_2024_1).id, json_body['calendar_months'].first['id']
      end

      it 'filters by years_months' do
        get :index, params: { f: { years_months: %w[2023-12 2024-1] } }, as: :json

        assert_response :success
        assert_equal 2, json_body['calendar_months'].count
        assert_equal 2, json_body['defaults'].count
      end
    end

    describe 'form_data' do
      it 'succeeds' do
        year = 2023
        month = 12

        get :form_data, params: { year: year, month: month }, as: :json

        assert_response :success
        assert_equal year, json_body['year']
        assert_equal month, json_body['month']
        refute_empty json_body['pl_full_time']['days']
        refute_empty json_body['pl_half_time']['days']
        refute_empty json_body['rb_full_time']['days']
      end
    end

    describe 'bulk_update' do
      let(:days) { { 1 => 4 } }

      it 'updates calendar month' do
        calendar_month = calendar_months(:milosz_2023_12)
        calendar_month_params = {
          user_id: calendar_month.user_id,
          year: calendar_month.year,
          month: calendar_month.month,
          days: days
        }

        travel_to Date.new(calendar_month.year, calendar_month.month, 1).prev_day do
          assert_changes -> { calendar_month.reload.days }, from: calendar_month.days, to: days do
            put :bulk_update, params: { calendar_months: [calendar_month_params] }, as: :json

            assert_response :created
          end
        end
      end

      it 'creates new calendar month' do
        date = Date.today.next_month
        calendar_month_params = {
          user_id: users(:milosz).id,
          year: date.year,
          month: date.month,
          days: days
        }

        assert_difference('CalendarMonth.count', 1) do
          put :bulk_update, params: { calendar_months: [calendar_month_params] }, as: :json

          assert_response :created
        end
      end

      it 'returns error if invalid params' do
        date = Date.today.next_month
        calendar_month_params = {
          user_id: users(:milosz).id,
          year: date.year,
          month: date.month,
          days: { invalid: 'param' }
        }

        assert_no_difference('CalendarMonth.count', 1) do
          put :bulk_update, params: { calendar_months: [calendar_month_params] }, as: :json

          assert_response :unprocessable_entity
        end
      end

      it 'returns error if missing params' do
        calendar_month_params = {
          user_id: users(:milosz).id,
          days: days
        }

        assert_no_difference('CalendarMonth.count', 1) do
          put :bulk_update, params: { calendar_months: [calendar_month_params] }, as: :json

          assert_response :bad_request
        end
      end
    end
  end
end
