require 'test_helper'

module Api
  module V1
    class PaymentSchedulesControllerTest < ActionController::TestCase
      setup do
        @user = users(:wik<PERSON>)
        authenticate(@user)
        @project = projects(:one)
      end

      test 'show' do
        get :show, params: { project_id: @project.id }, format: :json
        assert_response :success
        refute_empty json_body['payment_schedule']
        refute_empty json_body['starts_on']
        assert_equal [{ 'currency' => CURRENCIES[0].to_s, 'kind' => 'invoice', 'amount' => 2000 }], json_body['payment_schedule']['target_total_amount']
        assert_equal [{ 'currency' => CURRENCIES[0].to_s, 'kind' => 'invoice', 'amount' => 1500 }], json_body['payment_schedule']['target_current_amount']
        assert_operator json_body['payment_schedule']['payments_attributes'].first['issued_on'].to_date, :<, json_body['payment_schedule']['payments_attributes'].last['issued_on'].to_date
      end

      test 'show with issued_on_from filter' do
        get :show, params: { project_id: @project, f: { issued_on_from: Time.zone.today } },
                   format: :json

        assert_response :success
        assert_equal 2, json_body['payment_schedule']['payments_attributes'].count
        assert_not(json_body['payment_schedule']['payments_attributes'].detect do |payment_info|
          payment_info[:id] == payments(:one).id
        end)
      end

      test 'show with issued_on_to filter' do
        get :show, params: { project_id: @project, f: { issued_on_to: Time.zone.today } },
                   format: :json

        assert_response :success
        assert_equal 2, json_body['payment_schedule']['payments_attributes'].count
        assert_not(json_body['payment_schedule']['payments_attributes'].detect do |payment_info|
          payment_info[:id] == payments(:two).id
        end)
      end

      test 'show with sell_date_from filter' do
        get :show, params: { project_id: @project, f: { sell_date_from: Time.zone.today } },
                   format: :json

        assert_response :success
        assert_equal 2, json_body['payment_schedule']['payments_attributes'].count
        assert_not(json_body['payment_schedule']['payments_attributes'].detect do |payment_info|
          payment_info[:id] == payments(:one).id
        end)
      end

      test 'show with sell_date_to filter' do
        get :show, params: { project_id: @project, f: { sell_date_to: Time.zone.today } },
                   format: :json

        assert_response :success
        assert_equal 2, json_body['payment_schedule']['payments_attributes'].count
        assert_not(json_body['payment_schedule']['payments_attributes'].detect do |payment_info|
          payment_info[:id] == payments(:two).id
        end)
      end

      test 'show for non-existing payment schedule' do
        @project.payment_schedule.destroy

        get :show, params: { project_id: @project.id }, format: :json

        assert_response :success
      end

      test 'show shows payment with issued invoice higher on the list in case of the same date' do
        project = projects(:two)
        payment1 = payments(:three)
        payment2 = payments(:four)
        payment1.current_invoice.update_column(:payment_id, payment2.id)
        payment1.update_column(:issued_on, Time.zone.today)

        get :show, params: { project_id: project }, format: :json

        assert_equal payment2.id, json_body['payment_schedule']['payments_attributes'].first['id']
        assert_equal payment1.id, json_body['payment_schedule']['payments_attributes'].second['id']
      end

      test 'show shows shared payments as well' do
        project = projects(:three)
        payment = payments(:three)

        get :show, params: { project_id: project }, format: :json

        payment_info = json_body['payment_schedule']['payments_attributes'].detect do |data|
          data['id'] == payment.id
        end
        assert payment_info
        assert payment_info['shared']
      end

      test 'show xlsx format' do
        get :show, params: { project_id: projects(:two).id }, format: :xlsx
        assert_response :success
      end

      test 'edit works as expected' do
        project = projects(:two)

        get :edit, params: { project_id: project }, format: :json

        assert_response :success
        assert_not_empty json_body['payment_schedule']
        assert_not_empty json_body['starts_on']
        assert_not_empty json_body['kinds']
        assert_operator(
          json_body['payment_schedule']['payments_attributes'].first['issued_on'].to_date,
          :<,
          json_body['payment_schedule']['payments_attributes'].last['issued_on'].to_date
        )
      end

      test 'creates given valid params' do
        @project.payment_schedule.destroy
        mpk_number = mpk_numbers(:gui)
        mpk_number2 = mpk_numbers(:motion_design)
        params = {
          payments_attributes: [
            {
              issued_on: Date.today,
              sell_date: Date.today,
              predicted_amount: 10_000,
              mpk_positions_attributes: [
                {
                  mpk_number_id: mpk_number.id,
                  amount: 5000
                },
                {
                  mpk_number_id: mpk_number2.id,
                  amount: 5000
                }
              ]
            },
            {
              issued_on: Date.today + 1.month,
              sell_date: Date.today + 1.month,
              predicted_amount: 10_000,
              mpk_positions_attributes: [
                {
                  mpk_number_id: mpk_number.id,
                  project_id: @project.id,
                  amount: 5000
                },
                {
                  mpk_number_id: mpk_number2.id,
                  project_id: @project.id,
                  amount: 5000
                }
              ]
            }
          ]
        }
        assert_difference('PaymentSchedule.count') do
          assert_difference('Payment.count', 2) do
            post :create, params: { payment_schedule: params, project_id: @project.id },
                          format: :json
          end
        end
        assert_response :created
        refute_empty json_body['payments_attributes']
      end

      test 'does not create given invalid params' do
        @project.payment_schedule.destroy
        params = { payments_attributes: [{ issued_on: Date.today }] }
        assert_no_difference('PaymentSchedule.count') do
          post :create, params: { payment_schedule: params, project_id: @project.id },
                        format: :json
        end
        assert_response :unprocessable_entity
      end

      test 'updates payment schedule - before payment_schedules_lock_day' do
        travel_to Date.today.at_beginning_of_month do
          payment = payments(:one)
          payment.sell_date = Time.zone.now.to_date + 1.month
          mpk_number = mpk_numbers(:gui)
          @project.payment_schedule.payments = [payment]
          @project.payment_schedule.save!

          params = {
            payments_attributes:
              [
                {
                  id: payment.id,
                  issued_on: Date.today + 1.day,
                  sell_date: Date.today + 2.days,
                  predicted_amount: payment.mpk_positions.sum(:amount)
                },
                {
                  issued_on: Date.today + 5.days,
                  sell_date: Date.today + 5.days,
                  predicted_amount: 500,
                  mpk_positions_attributes: [{
                                               mpk_number_id: mpk_number.id,
                                               amount: 500
                                             }]
                }
              ]
          }

          assert_difference('Payment.count', 1) do
            patch :update, params: { payment_schedule: params, project_id: @project.id },
                  format: :json
          end
          assert_response :no_content
          payment.reload
          assert_equal Date.today + 1.day, payment.issued_on
          assert_equal Date.today + 2.days, payment.sell_date
        end
      end

      test 'updates payment schedule - after payment_schedules_lock_day - updated by global accounting' do
        Settings.payment_schedules_lock_day = Date.current.day + 1
        payment = payments(:one)
        @project.payment_schedule.payments = [payment]
        @project.payment_schedule.save!

        travel_to Date.new(2023, 7, 9) do
          Settings.payment_schedules_lock_day = 9
          payment.sell_date = Date.new(2023, 6, 5)
          mpk_number = mpk_numbers(:gui)

          @user.update(password_changed_at: 6.months.ago)
          authenticate(@user)

          params = {
            payments_attributes:
              [
                {
                  id: payment.id,
                  issued_on: Date.new(2023, 6, 11),
                  sell_date: Date.new(2023, 6, 12),
                  predicted_amount: payment.mpk_positions.sum(:amount)
                },
                {
                  issued_on: Date.today + 2.days,
                  sell_date: Date.today + 2.days,
                  predicted_amount: 500,
                  mpk_positions_attributes: [{
                                               mpk_number_id: mpk_number.id,
                                               amount: 500
                                             }]
                }
              ]
          }

          assert_difference('Payment.count', 1) do
            patch :update, params: { payment_schedule: params, project_id: @project.id },
                  format: :json
          end
          assert_response :no_content
          payment.reload
          assert_equal Date.new(2023, 6, 11), payment.issued_on
          assert_equal Date.new(2023, 6, 12), payment.sell_date
        end
      end

      test 'updates payment schedule - after payment_schedules_lock_day - updated by global admin' do
        user = users(:mkalita_global_admin_programmer)
        authenticate(user)

        Settings.payment_schedules_lock_day = Date.current.day + 1
        payment = payments(:one)
        @project.payment_schedule.payments = [payment]
        @project.payment_schedule.save!

        travel_to Date.new(2023, 8, 9) do
          Settings.payment_schedules_lock_day = 9
          payment.sell_date = Date.new(2023, 7, 5)
          payment.save!(validate: false)
          mpk_number = mpk_numbers(:gui)

          user.update(password_changed_at: 6.months.ago)

          params = {
            payments_attributes:
              [
                {
                  id: payment.id,
                  issued_on: Date.new(2023, 7, 11),
                  sell_date: Date.new(2023, 7, 12),
                  predicted_amount: payment.mpk_positions.sum(:amount)
                },
                {
                  issued_on: Date.today + 2.days,
                  sell_date: Date.today + 2.days,
                  predicted_amount: 500,
                  mpk_positions_attributes: [{
                                               mpk_number_id: mpk_number.id,
                                               amount: 500
                                             }]
                }
              ]
          }

          assert_difference('Payment.count', 1) do
            patch :update, params: { payment_schedule: params, project_id: @project.id },
                  format: :json
          end
          assert_response :no_content
          payment.reload
          assert_equal Date.new(2023, 7, 11), payment.issued_on
          assert_equal Date.new(2023, 7, 12), payment.sell_date
        end
      end

      test 'updates payment schedule - after payment_schedules_lock_day - not updated by user without manage_after_date? permission' do
        user = users(:milosz)
        user_role = user.global_roles.first
        user_role&.activities << 'payment_schedules:global_manage'
        user_role&.save!
        authenticate(user)

        Settings.payment_schedules_lock_day = Date.current.day + 1
        payment = payments(:one)
        @project.payment_schedule.payments = [payment]
        @project.payment_schedule.save!

        travel_to Date.new(2023, 9, 9) do
          Settings.payment_schedules_lock_day = 9

          mpk_number = mpk_numbers(:gui)
          payment.update_column(:sell_date, Date.new(2023, 8, 5))
          user.update(password_changed_at: 6.months.ago)

          params = {
            payments_attributes:
              [
                {
                  id: payment.id,
                  issued_on: Date.new(2023, 8, 11),
                  sell_date: Date.new(2023, 8, 12),
                  predicted_amount: payment.mpk_positions.sum(:amount)
                },
                {
                  issued_on: Date.today + 2.days,
                  sell_date: Date.today + 2.days,
                  predicted_amount: 500,
                  mpk_positions_attributes: [{
                                               mpk_number_id: mpk_number.id,
                                               amount: 500
                                             }]
                }
              ]
          }

          assert_difference('Payment.count', 0) do
            patch :update, params: { payment_schedule: params, project_id: @project.id },
                  format: :json
          end
          assert_response :no_content
          payment.reload
          assert_not_equal Date.new(2023, 8, 11), payment.issued_on
          assert_not_equal Date.new(2023, 8, 12), payment.sell_date
        end
      end

      test 'updates payment schedule - after payment_schedules_lock_day - updated by user without manage_after_date? permission future payment' do
        user = users(:milosz)
        user_role = user.global_roles.first
        user_role&.activities << 'payment_schedules:global_manage'
        user_role&.save!
        authenticate(user)

        Settings.payment_schedules_lock_day = Date.current.day + 1
        payment = payments(:one)
        @project.payment_schedule.payments = [payment]
        @project.payment_schedule.save!

        travel_to Date.new(2023, 9, 9) do
          Settings.payment_schedules_lock_day = 9

          mpk_number = mpk_numbers(:gui)
          payment.update_column(:sell_date, Date.new(2023, 10, 5))
          user.update(password_changed_at: 6.months.ago)

          params = {
            payments_attributes:
              [
                {
                  id: payment.id,
                  issued_on: Date.new(2023, 10, 11),
                  sell_date: Date.new(2023, 10, 12),
                  predicted_amount: payment.mpk_positions.sum(:amount)
                },
                {
                  issued_on: Date.today + 2.days,
                  sell_date: Date.today + 2.days,
                  predicted_amount: 500,
                  mpk_positions_attributes: [{
                                               mpk_number_id: mpk_number.id,
                                               amount: 500
                                             }]
                }
              ]
          }

          assert_difference('Payment.count', 1) do
            patch :update, params: { payment_schedule: params, project_id: @project.id },
                  format: :json
          end
          assert_response :no_content
          payment.reload
          assert_equal Date.new(2023, 10, 11), payment.issued_on
          assert_equal Date.new(2023, 10, 12), payment.sell_date
        end
      end

      test 'destroy payments in schedule via update' do
        payment = payments(:one)
        params = {
          payments_attributes: [{ id: payment.id, _destroy: true }]
        }
        assert_difference('Payment.count', -1) do
          patch :update, params: { payment_schedule: params, project_id: @project.id },
                         format: :json
        end
        assert_response :no_content
      end

      test 'destroys payment schedule' do
        assert_difference('PaymentSchedule.count', -1) do
          delete :destroy, params: { project_id: @project.id }, format: :json
        end
        assert_response :no_content
      end

      test 'creates schedule with cyclic payment' do
        mpk_number = mpk_numbers(:gui)
        mpk_number2 = mpk_numbers(:motion_design)

        @project.payment_schedule.destroy
        params = {
          payments_attributes: [
            {
              issued_on: Date.today,
              sell_date: Date.today,
              predicted_amount: 10_000,
              cyclic: true, cycle_length: 1,
              mpk_positions_attributes: [
                {
                  mpk_number_id: mpk_number.id,
                  amount: 5000
                },
                {
                  mpk_number_id: mpk_number2.id,
                  amount: 5000
                }
              ]
            }
          ]
        }

        assert_difference('Payment.count') do
          post :create, params: { payment_schedule: params, project_id: @project },
                        format: :json
        end
        payment = Payment.last
        assert payment.cyclic?
      end

      test 'adds mpk number to nested payment' do
        project = projects(:two)
        payment = payments(:four)
        mpk_number = mpk_numbers(:motion_design)
        payment.mpk_positions.destroy_all

        params = {
          payments_attributes: [
            {
              id: payment.id,
              mpk_positions_attributes: [
                {
                  mpk_number_id: mpk_number.id,
                  amount: 500
                }
              ]
            }
          ]
        }

        assert_difference('PaymentMpkPosition.count', 1) do
          patch :update, params: { payment_schedule: params, project_id: project }, format: :json
        end

        assert_response :no_content
      end

      test 'remove mpk number from nested payment' do
        project = projects(:two)
        payment = payments(:four)
        mpk_number = mpk_numbers(:motion_design)
        mpk_position2 = PaymentMpkPosition.create!(payment: payment, mpk_number: mpk_number, amount: 500)

        params = {
          payments_attributes: [
            {
              id: payment.id,
              mpk_positions_attributes: [
                {
                  id: mpk_position2.id,
                  mpk_number_id: mpk_number.id,
                  amount: 500,
                  _destroy: true
                }
              ]
            }
          ]
        }

        assert_difference('PaymentMpkPosition.count', -1) do
          patch :update, params: { payment_schedule: params, project_id: project }, format: :json
        end

        assert_equal 1, payment.mpk_positions.size
        assert_response :no_content
      end

      test 'predicted amount is not consistent with mpk positions' do
        project = projects(:two)
        payment = payments(:four)
        mpk_number = mpk_numbers(:motion_design)

        payment.invoices.destroy_all

        params = {
          payments_attributes: [
            {
              id: payment.id,
              mpk_positions_attributes: [
                {
                  mpk_number_id: mpk_number.id,
                  amount: 8000
                }
              ]
            }
          ]
        }

        assert_difference 'Payment.count', 0 do
          assert_difference 'PaymentMpkPosition.count', 0 do
            patch :update, params: { payment_schedule: params, project_id: project }, format: :json

            assert json_body['errors']['payments[0].predicted_amount'].present?
          end
        end
      end

      test 'updates payment with issued invoice before payment_schedules_lock_day' do
        user = users(:mikolaj)
        user_role = user.global_roles.first
        user_role&.activities << 'payment_schedules:global_manage'
        user_role&.save!
        authenticate(user)

        payment = payments(:four)
        payment.update_column(:sell_date, Date.new(2023, 8, 9))
        project = payment.payment_schedule.project
        project.memberships.create!(member: user, roles: [roles(:front)])

        travel_to Date.new(2023, 9, 9) do
          Settings.payment_schedules_lock_day = 11

          refute_empty payment.invoices

          new_description = "#{payment.description} foo"

          params = {
            payments_attributes: [
              {
                id: payment.id,
                description: new_description
              }
            ]
          }

          patch :update, params: { payment_schedule: params, project_id: payment.payment_schedule.project }, format: :json

          assert_equal new_description, payment.reload.description
          assert_response :no_content
        end
      end

      test 'not updates payment with issued invoice after payment_schedules_lock_day' do
        user = users(:mikolaj)
        user_role = user.global_roles.first
        user_role&.activities << 'payment_schedules:global_manage'
        user_role&.save!
        authenticate(user)

        payment = payments(:four)
        payment.update_column(:sell_date, Date.new(2023, 8, 9))
        project = payment.payment_schedule.project
        project.memberships.create!(member: user, roles: [roles(:front)])

        travel_to Date.new(2023, 9, 9) do
          Settings.payment_schedules_lock_day = 8

          refute_empty payment.invoices

          new_description = "#{payment.description} poo"

          params = {
            payments_attributes: [
              {
                id: payment.id,
                description: new_description
              }
            ]
          }

          patch :update, params: { payment_schedule: params, project_id: payment.payment_schedule.project }, format: :json

          assert_not_equal new_description, payment.reload.description
          assert_response :no_content
        end
      end

      test 'updates payment with issued invoice after payment_schedules_lock_day by admin user' do
        user = users(:mkalita_global_admin_programmer)
        user_role = user.global_roles.first
        user_role&.activities << 'payment_schedules:global_manage'
        user_role&.save!
        authenticate(user)

        payment = payments(:four)
        payment.update_column(:sell_date, Date.new(2023, 8, 9))

        travel_to Date.new(2023, 9, 9) do
          Settings.payment_schedules_lock_day = 8

          refute_empty payment.invoices

          new_description = "#{payment.description} poo"

          params = {
            payments_attributes: [
              {
                id: payment.id,
                description: new_description
              }
            ]
          }

          patch :update, params: { payment_schedule: params, project_id: payment.payment_schedule.project }, format: :json

          assert_equal new_description, payment.reload.description
          assert_response :no_content
        end
      end
    end
  end
end
