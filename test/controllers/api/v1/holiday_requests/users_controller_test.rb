require 'test_helper'

module Api
  module V1
    module HolidayRequests
      class UsersControllerTest < ActionController::TestCase
        before do
          authenticate(users(:wiktoria))
        end

        test 'users_list with no params returns 400' do
          get :users_list, format: :json

          assert_response :bad_request
        end

        test 'users_list with month and year returns all users' do
          get :users_list, params: { f: { year: Time.zone.now.year, month: Time.zone.now.month } },
                           format: :json

          assert_response :success
          assert_equal User.native.count, json_body.count
        end

        test 'users_list with term filter returns proper users' do
          get :users_list, params: { f: { year: Time.zone.now.year, month: Time.zone.now.month,
                                          term: 'wiktoria' } }, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'users_list with contract of employment filter works properly' do
          get :users_list, params: { f: {
            year: Time.zone.now.year, month: Time.zone.now.month, contract_of_employment: 'false'
          } }, format: :json

          assert_response :success
          assert_equal User.native.where(contract_of_employment: false).count, json_body.count
        end
      end
    end
  end
end
