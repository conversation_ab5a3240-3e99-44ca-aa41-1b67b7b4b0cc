require 'test_helper'

module Api
  module V1
    module B2B
      class CostInvoicesControllerTest < ActionController::TestCase
        let(:cost_invoice) { cost_invoices(:wiktoria_cost_invoice) }
        let(:cost_invoice_pending_department) { cost_invoices(:wilhelm_pending_department_cost_invoice) }
        let(:mikolaj_cost_invoice) { cost_invoices(:mikolaj_cost_invoice) }
        let(:john_cost_invoice) { cost_invoices(:john_cost_invoice) }
        let(:valid_params) do
          {
            contractor_id: contractors(:wiktoria_contractor).id,
            company_id: companies(:one).id,
            sell_date: Time.zone.today,
            due_date: Time.zone.today,
            invoice_date: Time.zone.today,
            project_id: projects(:one).id,
            auto_cost_projects: false,
            number: "#{Time.zone.today.to_fs(:sql)}/1",
            document: Rack::Test::UploadedFile.new('test/fixtures/files/sample.pdf',
                                                  'application/pdf'),
            cost_invoice_positions_attributes: [
              {
                name: 'Position',
                amount: 1,
                unit_price: 100,
                tax_rate: 0
              }
            ],
            hours_worked: 160
          }
        end

        before do
          @user = users(:wiktoria)
          project = projects(:two)
          authenticate(@user)
          response = {
            time_entries: [
              {
                id: 1, project: { id: project.redmine_id, identifier: project.identifier },
                user: { login: @user.username },
                hours: 100
              },
              {
                id: 2, project: { id: project.redmine_id, identifier: project.identifier },
                user: { login: @user.username },
                hours: 60
              }
            ],
            total_count: 2
          }
          click_up_response = {
            data: [
              {
                id: 1, user: { id: @user.click_up_id, email: @user.email },
                duration: 20000000,
                task_location: { folder_id: '548615486151' }
              },
              {
                id: 2, user: { id: @user.click_up_id, email: @user.email },
                duration: 8800000,
                task_location: { folder_id: '548651325486' }
              }
            ]
          }

          stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
            .with(query: hash_including('limit' => '1000'))
            .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

          stub_request(:get, "#{Settings.click_up_api.uri}/team/#{companies(:one).click_up_workspace_id}/time_entries")
            .with(
              query: {
                assignee: users(:mikolaj).click_up_id,
                start_date: (mikolaj_cost_invoice.sell_date.beginning_of_month.to_datetime.to_i * 1000) - 100,
                end_date: (mikolaj_cost_invoice.sell_date.end_of_month.to_datetime.end_of_day.to_i * 1000) + 100,
                include_location_names: true
              }
            )
            .to_return(body: click_up_response.to_json, headers: { 'Content-Type' => 'application/json' })

          users(:wiktoria).update(company: companies(:two))
        end

        test 'create cost invoice with positions properly' do
          assert_difference('CostInvoice.count') do
            assert_difference('CostInvoicePosition.count') do
              post :create, params: { cost_invoice: valid_params }, format: :json
            end
          end

          assert_response :created
        end

        test 'create cost invoice with unprivileged user' do
          user = users(:mikolaj)
          authenticate(user)

          assert_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params }, format: :json
          end

          assert_response :created
          cost_invoice = CostInvoice.find(json_body['id'])
          assert_equal contractors(:mikolaj_contractor).id, cost_invoice.contractor_id
        end

        test 'new returns form data' do
          get :new, format: :json

          assert_response :success
          assert_equal CostInvoicePosition.tax_rates.keys, assigns(:tax_rates)
        end

        test 'new returns information about whether user can add cost invoice globally' do
          get :new, format: :json

          assert json_body['global_create']
        end

        test 'show' do
          get :show, params: { id: cost_invoice }, format: :json

          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert_equal CostInvoicePosition.tax_rates.keys, assigns(:tax_rates)
        end

        test 'show with correct acceptor - chief' do
          get :show, params: { id: cost_invoice_pending_department }, format: :json

          acceptor = json_body['cost_invoice']['acceptor']
          department_chief = cost_invoice_pending_department.contractor.user.department.chief.full_name

          assert_response :success
          assert_equal cost_invoice_pending_department, assigns(:cost_invoice)
          assert_equal acceptor, department_chief
        end

        test 'show with correct acceptor - uber chief' do
          Absence.create({ user_id: cost_invoice_pending_department.contractor.user.department.chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

          get :show, params: { id: cost_invoice_pending_department }, format: :json

          acceptor = json_body['cost_invoice']['acceptor']
          department_uber_chief = cost_invoice_pending_department.contractor.user.department.uber_chief.full_name

          assert_response :success
          assert_equal cost_invoice_pending_department, assigns(:cost_invoice)
          assert_equal acceptor, department_uber_chief
        end

        test 'show with correct acceptor - supervisor' do
          department = cost_invoice_pending_department.contractor.user.department
          uber_chief = department.uber_chief
          department.supervisor = users(:mikolaj)
          department.save!
          Absence.create({ user_id: department.chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
          Absence.create({ user_id: uber_chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

          get :show, params: { id: cost_invoice_pending_department }, format: :json

          acceptor = json_body['cost_invoice']['acceptor']
          supervisor = users(:mikolaj).full_name

          assert_response :success
          assert_equal cost_invoice_pending_department, assigns(:cost_invoice)
          assert_equal acceptor, supervisor
        end

        test 'show with correct acceptor - financial users' do
          get :show, params: { id: cost_invoice }, format: :json

          acceptor = json_body['cost_invoice']['acceptor']
          financial_users = User.active.joins(:global_roles).where(global_roles: { notify_dms_controller_acceptances: true }).distinct.map(&:full_name).join(', ')

          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert_equal acceptor, financial_users
        end

        test 'update' do
          accounting_number = accounting_numbers(:two)
          params = { accounting_number_id: accounting_number.id }
          cost_invoice.update(state: :pending_department)

          patch :update, params: { cost_invoice: params, id: cost_invoice }, format: :json

          assert_response :no_content
          assert_equal accounting_number, cost_invoice.reload.cost_projects.first.accounting_number
        end

        test 'index with state: accepted filter returns accepted cost invoices' do
          cost_invoice.accept!

          get :index, params: { f: { state: 'accepted' } }, format: :json

          assert_response :success
          assert(assigns(:cost_invoices).all?(&:accepted?))
        end

        test 'index with state: pending filter returns pending cost invoices' do
          get :index, params: { f: { state: 'pending' } }, format: :json

          assert_response :success
          assert(assigns(:cost_invoices).all?(&:pending?))
        end

        test 'index with state: draft filter returns draft cost invoices' do
          get :index, params: { f: { state: 'draft' } }, format: :json

          assert_response :success
          assert(assigns(:cost_invoices).all?(&:draft?))
        end

        test 'index with state: draft,for_correction returns draft and for correction cost invoices' do
          cost_invoice = cost_invoices(:wiktoria_cost_invoice)
          cost_invoice.withdraw!

          get :index, params: { f: { state: %w[draft for_correction] } }, format: :json

          assert_response :success
          assert_includes(assigns(:cost_invoices), cost_invoice)
        end

        test 'index with state: pending_department filter returns pending_department cost_invoices' do
          get :index, params: { f: { state: 'pending_department' } }, format: :json

          assert_response :success
          assert(assigns(:cost_invoices).all?(&:pending_department?))
        end

        test 'index with my: true filter returns only my cost invoices' do
          get :index, params: { f: { my_documents: true } }, format: :json

          assert_response :success
          assert_not(assigns(:cost_invoices).include?(cost_invoices(:mikolaj_cost_invoice)))
        end

        test 'index with contractor_username filter returns only cost invoices of given user' do
          user = users(:mikolaj)

          get :index, params: { f: { contractor_username: user.username } }, format: :json

          assert_response :success
          assert(assigns(:cost_invoices).all? { |ci| ci.contractor.user == user })
        end

        test 'api key access to index' do
          api_key = ApiKey.create(name: 'Redmine')
          @request.headers['X-Api-Key'] = api_key.key

          get :index, format: :json

          assert_response :success
          assert(assigns(:cost_invoices).any?)
        end

        test 'index with date_to filters cost invoices by invoice date' do
          date_to = Date.parse('2020-02-16')

          get :index, params: { f: { date_to: date_to } }, format: :json

          assert_response :success
          assert_equal ::B2B::CostInvoice.where('cost_invoices.invoice_date <= ?', date_to).count,
                       assigns(:cost_invoices).count
        end

        test 'index with date_from filters cost invoices by invoice date' do
          date_from = Date.parse('2020-02-18')

          get :index, params: { f: { date_from: date_from } }, format: :json

          assert_response :success
          assert_equal ::B2B::CostInvoice.where('cost_invoices.invoice_date >= ?', date_from).count,
                       assigns(:cost_invoices).count
        end

        test 'index with user_id filters cost invoices by contractor' do
          contractor = contractors(:mikolaj_contractor)

          get :index, params: { f: { contractor_id: contractor } }, format: :json

          assert_response :success
          assert_equal ::B2B::CostInvoice.where(contractor_id: contractor.id).count,
                       assigns(:cost_invoices).count
        end

        test 'index with user_id filters cost invoices by mpk number' do
          mpk_number = mpk_numbers(:other)
          ::B2B::CostInvoice.last.contractor.update(user: mpk_number.departments.first.users.first)

          get :index, params: { f: { mpk_number_id: mpk_number.id } }, format: :json

          assert_response :success
          assert_equal ::B2B::CostInvoice.joins(contractor: { user: { department: :mpk_number } })
                                      .where(mpk_numbers: { id: mpk_number.id }).count,
                       assigns(:cost_invoices).count
        end

        test 'index as regular user returns proper invoices' do
          authenticate(users(:mikolaj))

          get :index, format: :json

          assert_response :success
          assert_equal 2, json_body.count
        end

        test 'accept sets invoice to accepted' do
          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.accepted?
        end

        test 'accept pending_department invoice sets invoice to pending' do
          authenticate(users(:mikolaj))
          users(:wilhelm).update(redmine_id: 12345)
          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)

          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.pending?
        end

        test 'successful acceptation of pending_department invoice by supervisor' do
          supervisor = users(:wilhelm)
          chief = users(:mikolaj)
          uber_chief = users(:wiktoria)
          users(:wilhelm).update(redmine_id: 12345)

          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)
          department = cost_invoice.contractor.user.department
          department.supervisor = supervisor
          department.save!

          travel_to(Time.zone.local(2023, 12, 14, 12, 0, 0))

          HolidayRequest.create!(applicant: chief, starts_on: Date.current, ends_on: Date.current,
                                 created_by_user_id: uber_chief.id, updated_by_user_id: uber_chief.id,
                                 modified_by_user_at: Time.current)
          HolidayRequest.create!(applicant: uber_chief, starts_on: Date.current, ends_on: Date.current,
                                 created_by_user_id: uber_chief.id, updated_by_user_id: uber_chief.id,
                                 modified_by_user_at: Time.current)

          authenticate(supervisor)

          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)

          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.pending?
        end

        test 'unsuccessful self-acceptation of pending_department invoice of chief' do
          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)
          contractor_user = cost_invoice.contractor.user
          contractor_user.department.update(chief: contractor_user)
          authenticate(contractor_user)

          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :forbidden
          assert cost_invoice.reload.pending_department?
        end

        test 'successful acceptation of pending_department invoice of chief by uber' do
          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)
          contractor_user = cost_invoice.contractor.user
          uber_chief = users(:mikolaj)
          contractor_user.department.update(chief: contractor_user, uber_chief: uber_chief)
          authenticate(uber_chief)
          users(:wilhelm).update(redmine_id: 12345)

          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.pending?
        end

        test 'unsuccessful self-acceptation of pending_department invoice of uber chief' do
          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)
          contractor_user = cost_invoice.contractor.user
          contractor_user.department.update(uber_chief: contractor_user)
          authenticate(contractor_user)

          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :forbidden
          assert cost_invoice.reload.pending_department?
        end

        test 'successful acceptation of pending_department invoice of uber chief by board member' do
          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)
          contractor_user = cost_invoice.contractor.user
          contractor_user.department.update(uber_chief: contractor_user)
          board_member = users(:milosz)
          board_member.department.update(board_member: true)
          authenticate(board_member)
          users(:wilhelm).update(redmine_id: 12345)

          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.pending?
        end

        test 'invoice force accept' do
          patch :accept, params: { id: mikolaj_cost_invoice, force_accept: true }, format: :json

          assert_response :no_content
          assert mikolaj_cost_invoice.reload.accepted?
        end

        test 'invoice acceptation without accepted contractor' do
          mikolaj_cost_invoice.contractor.update(state: :pending)

          patch :accept, params: { id: mikolaj_cost_invoice, force_accept: true }, format: :json

          assert_response :bad_request
          assert_includes json_body['failures'], 'contractor_active?'
        end

        test 'invoice acceptation with locked accounting number' do
          cost_invoice.update_columns(auto_cost_projects: true)
          projects(:two).update_columns(accounting_number_id: accounting_numbers(:locked).id)

          patch :accept, params: { id: cost_invoice }, format: :json

          assert_response :bad_request
          assert_includes json_body['failures'], 'generate_cost_projects'
        end

        test 'invoice acceptation with locked accounting number force accept' do
          cost_invoice.update_columns(auto_cost_projects: true)
          projects(:two).update_columns(accounting_number_id: accounting_numbers(:locked).id)

          patch :accept, params: { id: cost_invoice, force_accept: true }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.accepted?
        end

        test 'accept invoice when user should not report time' do
          users(:board_member_user).update(redmine_id: 4685)
          patch :accept, params: { id: john_cost_invoice }, format: :json

          assert_response :no_content
          assert john_cost_invoice.reload.accepted?
        end

        test 'accept invoice when user\'s department should not report time' do
          mikolaj_cost_invoice.contractor.user.department.update(time_reports_not_required: true)

          patch :accept, params: { id: mikolaj_cost_invoice }, format: :json

          assert_response :no_content
          assert mikolaj_cost_invoice.reload.accepted?
        end

        test 'withdraw sets invoice to for_correction' do
          patch :withdraw, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.for_correction?
        end

        test 'reject sets invoice to rejected' do
          patch :reject, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.rejected?
        end

        test 'reject pending_department invoice sets invoice as rejected' do
          authenticate(users(:mikolaj))
          cost_invoice = cost_invoices(:wilhelm_pending_department_cost_invoice)

          patch :reject, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.rejected?
        end

        test 'GET #document works properly' do
          get :document, params: { id: cost_invoice }, format: :json

          assert_response :success
          assert_equal @response.headers['Content-Transfer-Encoding'], 'binary'
          assert_match "inline; filename=\"#{cost_invoice.document.original_filename}\"",
                       @response.headers['Content-Disposition']
        end

        test 'GET #history returns snapshots' do
          # create snapshot:
          post :create, params: { cost_invoice: valid_params.merge(send_to_controller: true) },
                        format: :json
          id = JSON.parse(response.body)['id']

          get :history, params: { id: id }, format: :json

          assert_response :success
          assert_equal 1, JSON.parse(response.body)['history_records'].count

          history_record = JSON.parse(response.body)['history_records'].first
          assert_equal 'create', history_record['action']
          assert_nil history_record['comment']
          assert_equal 'new', history_record['state_was']
        end

        test 'PATCH #recall recalls invoice from accepted' do
          cost_invoice = cost_invoices(:wiktoria_cost_invoice)
          cost_invoice.auto_cost_projects = false
          cost_invoice.enable_force_accept
          cost_invoice.accounting_number_id = accounting_numbers(:one)
          cost_invoice.accept!

          patch :recall, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.pending?
        end

        test 'POST #perform_daily_worker' do
          scheduled_set_mock = mock
          scheduled_set_mock.expects(:any?).with_block_given.returns(false)
          Sidekiq::ScheduledSet.expects(:new).returns(scheduled_set_mock)
          CostInvoicesDailyWorker.expects(:perform_in).with(10.seconds, 'B2B', Date.current.to_s)

          post :perform_daily_worker

          assert_response :no_content
        end

        test 'POST #ocr calls ComarchOcr service and returns correct recognized data' do
          recognized_data = {
            Fields: {
              DocumentNumber: 'FV/1/09/2022',
              DateOfIssue: '2022-09-30',
              DateOfSale: '2022-09-30',
              DueDate: '2022-10-14',
              PaymentForm: 'przelew',
              BankAccountNumber: 'BankAccountNumber'
            },
            IsCorrection: false,
            SellerContractor: {
              TIN: 'TIN',
              CompanyName: 'CompanyName',
              Street: 'Street',
              StreetNumber: 'StreetNumber',
              PostCode: 'PostCode',
              City: 'Warszawa',
              Voivodeship: 'MAZOWIECKIE',
            },
            BuyerContractor: {
              TIN: '**********',
              CompanyName: 'EFIGENCE SPÓŁKA AKCYJNA',
              Street: 'ul. Wołoska',
              StreetNumber: '9 A',
              PostCode: '02-583',
              City: 'Warszawa',
              Voivodeship: 'MAZOWIECKIE',
            },
            Currency: 'PLN',
            ProductItems: [
              {
                Name: 'Usługi dodatkowe',
                Unit: 'szt',
                Count: 1.0,
                NettoUnitPrice: 250.0,
                BruttoUnitPrice: 307.5,
                Netto: 250.0,
                Brutto: 307.5,
                VatRate: 23.0,
                VatAmount: 57.5
              }
            ]
          }

          ComarchOcr.expects(:process_invoice).returns(recognized_data)

          post :ocr, params: { document: valid_params[:document] }, format: :json

          assert_response :success
          result = json_body['recognized_data']
          assert_equal recognized_data[:Fields][:DocumentNumber], result['number']
          assert_equal 'transfer', result['payment_method']
          assert_equal recognized_data[:SellerContractor][:TIN], result['seller']['vat_number']
          assert_equal recognized_data[:BuyerContractor][:CompanyName], result['buyer']['name']
          assert_equal recognized_data[:ProductItems].first[:Name], result['cost_invoice_positions_attributes'].first['name']
        end

        test 'POST #ocr calls ComarchOcr service and returns correct recognized data and contractor' do
          contractor = contractors(:wiktoria_contractor)
          recognized_data = {
            Fields: {
              DocumentNumber: 'FV/1/09/2022',
              DateOfIssue: '2022-09-30',
              DateOfSale: '2022-09-30',
              DueDate: '2022-10-14',
              PaymentForm: 'przelew',
              BankAccountNumber: contractor.account_number
            },
            IsCorrection: false,
            SellerContractor: {
              TIN: contractor.vat_number,
              CompanyName: 'CompanyName',
              Street: 'Street',
              StreetNumber: 'StreetNumber',
              PostCode: 'PostCode',
              City: 'Warszawa',
              Voivodeship: 'MAZOWIECKIE',
            },
            BuyerContractor: {
              TIN: '**********',
              CompanyName: 'EFIGENCE SPÓŁKA AKCYJNA',
              Street: 'ul. Wołoska',
              StreetNumber: '9 A',
              PostCode: '02-583',
              City: 'Warszawa',
              Voivodeship: 'MAZOWIECKIE',
            },
            Currency: 'PLN',
            ProductItems: [
              {
                Name: 'Usługi dodatkowe',
                Unit: 'szt',
                Count: 1.0,
                NettoUnitPrice: 250.0,
                BruttoUnitPrice: 307.5,
                Netto: 250.0,
                Brutto: 307.5,
                VatRate: 23.0,
                VatAmount: 57.5
              }
            ]
          }

          ComarchOcr.expects(:process_invoice).returns(recognized_data)

          post :ocr, params: { document: valid_params[:document] }, format: :json

          assert_response :success
          result = json_body['recognized_data']

          assert_equal recognized_data[:Fields][:DocumentNumber], result['number']
          assert_equal 'transfer', result['payment_method']
          assert_equal recognized_data[:SellerContractor][:TIN], result['seller']['vat_number']
          assert_equal recognized_data[:BuyerContractor][:CompanyName], result['buyer']['name']
          assert_equal recognized_data[:ProductItems].first[:Name], result['cost_invoice_positions_attributes'].first['name']
        end
      end
    end
  end
end
