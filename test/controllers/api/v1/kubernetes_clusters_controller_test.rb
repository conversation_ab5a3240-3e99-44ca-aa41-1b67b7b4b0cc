require 'test_helper'

class Api::V1::KubernetesClustersControllerTest < ActionController::TestCase
  let(:api_key) { ApiKey.create(name: 'test key') }

  test "index returns all kubernetes clusters' id, name and production after sign-in as an user" do
    authenticate(users(:milosz))

    get :index, format: :json

    assert_response :success
    assert_equal response.body, KubernetesCluster.all.to_json(only: [:id, :name, :production])
  end

  test "index returns all kubernetes clusters' id, name and production flag after sign-in as an api-key" do
    @request.headers['X-Api-Key'] = api_key.key

    get :index, format: :json

    assert_response :success
    assert_equal response.body, KubernetesCluster.all.to_json(only: [:id, :name, :production])
  end

  test 'index allows filtering by project name' do
    @request.headers['X-Api-Key'] = api_key.key

    get :index, params: { f: { project_identifier: projects(:one).identifier } }, format: :json

    assert_response :success
    assert_equal response.body, [kubernetes_clusters(:one)].to_json(only: [:id, :name, :production])
  end

  test 'keys returns all keys for a signed-in user' do
    authenticate(users(:milosz))

    get :keys, format: :json

    assert_response :success
    assert_equal response.body, [kubernetes_clusters(:one)].to_json(only: [:name, :key_url])
  end
end
