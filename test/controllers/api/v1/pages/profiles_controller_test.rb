require 'test_helper'

module Api
  module V1
    # debug sql:
    # tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
    class Api::V1::Pages::ProfilesControllerTest < ActionController::TestCase
      include Minitest::XSwaggerSignInAs

      fixtures :global_roles, :user_global_roles, :users

      setup do
        @profile = ::Pages::Profile.new(users(:mkalita_user))
      end

      def test_rescues_module_inclusion
        assert_includes Api::V1::Pages::ProfilesController.ancestors, Api::V1::Concerns::Handlers::Rescues
      end

      test 'should show user profile' do
        get :show, params: { id: @profile }, format: :json
        assert_response :success
        assert_includes json_body, 'url'
        assert_includes json_body, 'first_name'
        assert_includes json_body, 'last_name'
        assert_includes json_body, 'email'
        assert_includes json_body, 'projects'
        assert_includes json_body, 'approvals'
      end

      test 'should update user profile' do
        put :update, params: { id: @profile,
                               profile: { current_password: default_password,
                                           username: @profile.user.username,
                                           email: @profile.user.email } },
                     format: :json
        assert_response 204, @response.body.to_s
      end

      test 'should require current_password for udpate' do
        put :update, params: { id: @profile,
                               profile: { username: @profile.user.username,
                                          email: @profile.user.email } },
                     format: :json
        assert_response 422, @response.body.to_s
        assert_includes json_body['errors']['current_password'], 'can\'t be blank'
      end

      test 'should require correct current_password for udpate' do
        put :update, params: { id: @profile,
                               profile: { current_password: '',
                                           username: @profile.user.username,
                                           email: @profile.user.email } },
                     format: :json
        assert_response 422, @response.body.to_s
        assert_includes json_body['errors']['current_password'], 'can\'t be blank'

        put :update, params: { id: @profile,
                               profile: { current_password: 'asd',
                                           username: @profile.user.username,
                                           email: @profile.user.email } },
                     format: :json
        assert_response 422, @response.body.to_s
        assert_includes json_body['errors']['current_password'], 'is invalid'
      end

      test 'dismisses onboarding successfully' do
        patch :dismiss_onboarding, params: { id: @profile }, format: :json
        assert @profile.user.reload.dismiss_onboarding?
      end

      test 'works with oauth token' do
        @request.headers['X-Swagger-Sign-In-As'] = nil
        user = users(:wiktoria)
        access_token = Doorkeeper::AccessToken.create!(
          resource_owner_id: user.id
        )

        @request.headers.merge!({ 'Authorization' => "Bearer #{access_token.token}" })

        get :show, format: :json
        assert_response :success
      end

      test 'save_preferences' do
        user = users(:mkalita_user)
        preferences = { users_index: { state: '0' } }.with_indifferent_access

        patch :save_preferences, params: { preferences: preferences }, format: :json

        assert_response :success
        assert_equal user.reload.preferences, preferences
      end

      def test_show_absence_quota_and_absence_balance
        get :show, params: { id: @profile }, format: :json
        assert_response :success
        assert_includes json_body, 'absence_quota'
        assert_includes json_body, 'absence_balance'
      end

      def test_service_password
        salt = SecureRandom.hex(16)

        user = users(:mkalita_user)
        sign_in(user)
        SecureRandom.stub(:hex, salt) do
          created_user = User.create(username: 'mrnotlocked',
                                     first_name: 'John',
                                     last_name: 'Locked',
                                     email: '<EMAIL>',
                                     password: '123qweR!',
                                     password_confirmation: '123qweR!',
                                     dev_password: 'ownASD123')
          refute created_user.errors.any?

          dev_password = Base64.strict_encode64(Digest::SHA256.digest('ownASD123' + salt) + salt)
          assert_equal dev_password, created_user.hashed_passwords['dev']['ssha256']
          patch :update, params: { profile: { current_password: default_password,
                                              dev_password: 'own2ASD123' } }, format: :json
          assert_response 204, @response.body.to_s
          created_user.reload
          refute created_user.errors.any?
          assert_equal created_user.hashed_passwords['dev']['ssha256'], dev_password
        end
      end

      def test_service_password_strength_validation
        user = users(:mkalita_user)
        sign_in(user)
        patch :update, params: { id: '', profile: { current_password: default_password,
                                                    dev_password: 'own' } }, format: :json
        assert_response 422, @response.body.to_s
        assert json_body['errors']['dev_password']
        patch :update, params: { id: '', profile: { current_password: default_password,
                                                    dev_password: '' } }, format: :json
        assert_response 422, @response.body.to_s
        assert json_body['errors']['dev_password']
        patch :update, params: { id: '', profile: { current_password: default_password,
                                                    dev_password: '1235678' } }, format: :json
        assert_response 422, @response.body.to_s
        assert json_body['errors']['dev_password']
      end

      def test_show_authorization
        viewer = users(:mkalita_user)
        sign_in(viewer)
        get :show, format: :json
        assert_response 200, @response.body.to_s
      end

      def test_update_authorization
        viewer = users(:mkalita_user)
        sign_in(viewer)
        patch :update, params: { profile: { username: 'unlocked',
                                            current_password: 'INVALID PASSWORD' } }, format: :json
        assert_response 422, @response.body.to_s
      end
    end
  end
end
