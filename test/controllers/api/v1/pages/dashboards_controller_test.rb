require 'test_helper'

module Api
  module V1
    module Pages
      class DashboardsControllerTest < ActionController::TestCase
        describe Api::V1::Pages::DashboardsController do
          context 'when not signed in' do
            describe 'show' do
              it 'returns unauthorized' do
                get :show, format: :json

                assert_response :unauthorized
              end
            end
          end

          context 'when signed in' do
            before do
              authenticate(users(:wik<PERSON>))
            end

            describe 'show' do
              it 'returns success' do
                get :show, format: :json

                assert_response :success
              end

              it 'returns dashboard data' do
                get :show, format: :json

                dashboard = json_body
                assert_includes dashboard, 'show_b2b_reminder'
                assert_includes dashboard, 'show_payments_reminder'
                assert_includes dashboard, 'show_new_hr_cost_invoice_shortcut'
                assert_includes dashboard, 'show_new_dms_cost_invoice_shortcut'
                assert_includes dashboard, 'show_new_holiday_request_shortcut'
                assert_includes dashboard, 'show_new_admin_ticket_shortcut'
                assert_includes dashboard, 'show_new_asset_shortcut'
                assert_includes dashboard, 'show_available_holidays'
                assert_includes dashboard, 'show_my_holiday_requests'
                assert_includes dashboard, 'show_my_dms_cost_invoices'
                assert_includes dashboard, 'show_teams_assets_requests'
                assert_includes dashboard, 'show_employees_dms_cost_invoices'
                assert_includes dashboard, 'show_employees_hr_cost_invoices'
                assert_includes dashboard, 'show_employees_next_weeks_holidays'
                assert_includes dashboard, 'show_employees_holiday_requests'
                assert_includes dashboard, 'show_employees_assets_requests'
              end

              [
                %w[payments_to_remind show_payments_reminder],
                %w[available_holidays show_available_holidays],
                %w[my_holiday_requests show_my_holiday_requests],
                %w[my_dms_cost_invoices show_my_dms_cost_invoices],
                %w[teams_assets_requests show_teams_assets_requests],
                %w[employees_dms_cost_invoices show_employees_dms_cost_invoices],
                %w[employees_hr_cost_invoices show_employees_hr_cost_invoices],
                %w[employees_next_weeks_holidays show_employees_next_weeks_holidays],
                %w[employees_holiday_requests show_employees_holiday_requests],
                %w[employees_assets_requests show_employees_assets_requests]
              ].each do |attr, show_attr|
                it "includes #{attr} if #{show_attr}" do
                  ::Pages::Dashboard.any_instance.stubs(show_attr).returns(true)

                  get :show, format: :json
                  assert_includes json_body, attr
                end

                it "does not include #{attr} if not #{show_attr}" do
                  ::Pages::Dashboard.any_instance.stubs(show_attr).returns(false)

                  get :show, format: :json
                  refute_includes json_body, attr
                end
              end
            end
          end
        end
      end
    end
  end
end
