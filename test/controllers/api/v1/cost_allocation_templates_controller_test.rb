require 'test_helper'

module Api
  module V1
    class CostAllocationTemplatesControllerTest < ActionController::TestCase
      let(:user) { users(:wiktoria) }
      let(:valid_params) do
        {
          name: 'Template name',
          cost_allocation_template_positions_attributes: [
            {
              department_id: departments(:two).id,
              accounting_number_id: accounting_numbers(:two).id,
              share_amount: 95.0
            },
            {
              department_id: departments(:two).id,
              accounting_number_id: accounting_numbers(:one).id,
              share_amount: 5.0
            }
          ]
        }
      end

      before do
        authenticate(user)
      end

      test 'index' do
        get :index, format: :json

        assert_response :success
        assert_same_elements user.cost_allocation_templates.ids, json_body.pluck('id')
      end

      test 'create' do
        assert_difference('user.cost_allocation_templates.count', 1) do
          post :create, params: { cost_allocation_template: valid_params }, format: :json
        end
        assert_response :created
        assert_equal valid_params[:name], json_body['name']
      end

      test 'update' do
        cost_allocation_template = cost_allocation_templates(:wiktoria_template)
        cost_allocation_template_position = cost_allocation_template.cost_allocation_template_positions.first
        attributes = {
          name: 'New name',
          cost_allocation_template_positions_attributes: [
            {
              id: cost_allocation_template_position.id,
              department_id: departments(:one).id
            }
          ]
        }

        patch :update, params: { id: cost_allocation_template, cost_allocation_template: attributes }, format: :json

        assert_response :no_content
        assert_equal attributes[:name], cost_allocation_template.reload.name
        assert_equal attributes[:cost_allocation_template_positions_attributes].first[:department_id],
                     cost_allocation_template_position.reload.department_id
      end

      test 'destroy' do
        cost_allocation_template = cost_allocation_templates(:wiktoria_template)
        assert_difference('user.cost_allocation_templates.count', -1) do
          delete :destroy, params: { id: cost_allocation_template.id }, format: :json
        end
        assert_response :no_content
      end

      test 'destroy - not authorized' do
        authenticate(users(:internal_user))
        cost_allocation_template = cost_allocation_templates(:wiktoria_template)

        assert_difference('user.cost_allocation_templates.count', 0) do
          delete :destroy, params: { id: cost_allocation_template.id }, format: :json
        end
        assert_response :not_found
        assert cost_allocation_template.reload.valid?
      end

      test 'update allows to destroy cost allocation template positions' do
        cost_allocation_template = cost_allocation_templates(:wiktoria_template)
        attributes = {
          cost_allocation_template_positions_attributes: [
            *cost_allocation_template.cost_allocation_template_positions.map do |position|
              position.slice(:id, :department_id, :accounting_number_id, :share_amount)
                      .merge(_destroy: true)
            end,
            {
              department_id: departments(:two).id,
              accounting_number_id: accounting_numbers(:two).id,
              share_amount: 100
            }
          ]
        }

        patch :update, params: { id: cost_allocation_template, cost_allocation_template: attributes }, format: :json

        assert_response :no_content
        assert_equal 1, cost_allocation_template.cost_allocation_template_positions.count
      end
    end
  end
end
