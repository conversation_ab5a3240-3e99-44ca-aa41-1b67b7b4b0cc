require 'test_helper'

module Api
  module V1
    class DashboardsControllerTest < ActionController::TestCase
      before do
        authenticate(users(:wik<PERSON>))
      end

      test 'show' do
        get :show, format: :json

        assert_response :success
        assert_includes json_body, 'invoice_acceptances'
        assert_includes json_body, 'cost_invoice_acceptances'
      end
    end
  end
end
