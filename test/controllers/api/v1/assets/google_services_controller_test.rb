require 'test_helper'

class Api::V1::Assets::GoogleServicesControllerTest < ActionController::TestCase
  setup do
    @user = users(:wik<PERSON>)
    project = projects(:one)
    authenticate(@user)
    @asset = GoogleService.create!(project: project, name: 'budogram',
                                   requester: @user, requested_date: Time.zone.now,
                                   g_account: '<EMAIL>', g_maps: true)
    @valid_params = {
      project_id: project.id,
      g_account: '<EMAIL>',
      g_mail_account: true
    }
  end

  test 'asset is created given valid params' do
    assert_difference('GoogleService.count') do
      post :create, params: { google_service: @valid_params }, format: :json
    end

    assert_response :created
  end

  test 'validation fails properly on create' do
    post :create, params: { google_service: @valid_params.merge(g_account: '') }, format: :json
    assert_response :unprocessable_entity
  end

  test 'returns asset' do
    post :show, params: { id: @asset.id }, format: :json
    assert_equal json_body["id"], @asset.id
    assert_response :ok
  end
end
