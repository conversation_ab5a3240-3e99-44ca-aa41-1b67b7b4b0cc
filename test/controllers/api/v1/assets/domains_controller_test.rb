require 'test_helper'

class Api::V1::Assets::DomainsControllerTest < ActionController::TestCase
  setup do
    @user = users(:wiktoria)
    project = projects(:one)
    authenticate(@user)
    @asset = Domain.create!(project: project,
                            requester: @user,
                            requested_date: Time.zone.now,
                            notes: 'My awesome notes', ssl_type: 0,
                            name: 'www.wp.pl')
    @valid_params = {
      project_id: project.id,
      name: 'budogram.pl',
    }
  end

  test 'asset is created given valid params' do
    assert_difference('Domain.count') do
      post :create, params: { domain: @valid_params }, format: :json
    end

    assert_response :created
  end

  test 'validation fails properly on create' do
    post :create, params: { domain: @valid_params.merge(name: '') }, format: :json
    assert_response :unprocessable_entity
  end

  test 'returns asset' do
    post :show, params: { id: @asset.id }, format: :json
    assert_equal json_body["id"], @asset.id
    assert_response :ok
  end
end
