require 'test_helper'

class Api::V1::Assets::ServersControllerTest < ActionController::TestCase
  setup do
    @user = users(:wiktoria)
    project = projects(:one)
    authenticate(@user)
    @asset = Server.create!(project: project, name: 'onet.pl',
                            requester: @user, requested_date: Time.zone.now,
                            notes: 'My awesome notes',
                            technology: 'tomcat8',
                            environment: 'prod',
                            ram_count: 1,
                            storage: 1, db_type: 'mysql',
                            db_size: 100)
    @valid_params = {
      project_id: project.id,
      technology: 'tomcat8',
      ram_count: 1000,
      storage: 40_000,
      send_emails: true,
      receivers_addresses: '<EMAIL>',
      db_size: 100_000,
      db_type: 'mysql',
      environment: 'prod'
    }
  end

  test 'server is created given valid params' do
    assert_difference('Server.count') do
      post :create, params: { server: @valid_params }, format: :json
    end

    assert_response :created
  end

  test 'validation fails properly on create' do
    post :create, params: { server: @valid_params.merge(project_id: '') }, format: :json
    assert_response :unprocessable_entity
  end

  test 'returns asset' do
    get :show, params: { id: @asset.id }, format: :json
    assert_equal json_body['id'], @asset.id
    assert_response :ok
  end
end
