require 'test_helper'

class Api::V1::Assets::KubernetesNamespacesControllerTest < ActionController::TestCase
  let(:project) { projects(:one) }
  let(:valid_attributes) do
    {
      project_id: project,
      kubernetes_cluster_id: kubernetes_clusters(:one).id,
      ram_count: 5,
      storage: 5,
      vcpu_count: 5,
      name: 'dev-namespace',
    }
  end
  let(:api_key) { ApiKey.create(name: 'test key') }
  let(:namespace) { kubernetes_namespaces(:cluster_one_project_one) }

  teardown do
    @request.headers['X-Api-Key'] = nil
  end

  test 'namespace is created given valid params' do
    authenticate(users(:wiktoria))

    assert_difference -> { KubernetesNamespace.count } do
      post :create, params: { kubernetes_namespace: valid_attributes }, format: :json
    end

    assert_response :created
  end

  test 'validation fails properly on create' do
    authenticate(users(:wiktoria))

    post :create, params: { kubernetes_namespace: valid_attributes.merge(project_id: '') }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['project']
  end

  test 'show returns namespace' do
    authenticate(users(:wiktoria))

    get :show, params: { id: namespace.id }, format: :json

    assert_response :success
    assert_equal json_body['id'], namespace.id
  end

  test 'show works for api key' do
    @request.headers['X-Api-Key'] = api_key.key

    get :show, params: { id: namespace.id }, format: :json

    assert_response :success
    assert_equal json_body['id'], namespace.id
    assert_not_empty json_body['users']
  end

  test 'index works for api key' do
    @request.headers['X-Api-Key'] = api_key.key

    get :index, format: :json

    assert_response :success
    assert_equal KubernetesNamespace.count, json_body.count
  end

  test 'index filters by cluster_name' do
    @request.headers['X-Api-Key'] = api_key.key

    get :index, params: { kubernetes_cluster_name: kubernetes_clusters(:one).name }, format: :json

    assert_response :success
    assert_equal [kubernetes_namespaces(:cluster_one_project_one).id], json_body.map { |n| n['id'] }
  end

  test 'index filters by project identifier' do
    @request.headers['X-Api-Key'] = api_key.key

    get :index, params: { project_identifier: projects(:one).identifier }, format: :json

    assert_response :success

    assert_equal [kubernetes_namespaces(:cluster_one_project_one).id], json_body.map { |n| n['id'] }
  end

  test 'index filters by asset state' do
    @request.headers['X-Api-Key'] = api_key.key

    get :index, params: { state: 'closed' }, format: :json

    assert_response :success
    assert_equal [kubernetes_namespaces(:cluster_two_project_two).id], json_body.map { |n| n['id'] }
  end

  test 'index filters by name' do
    @request.headers['X-Api-Key'] = api_key.key

    get :index, params: { name: 'mystring-cluster-two' }, format: :json

    assert_response :success
    assert_equal [kubernetes_namespaces(:cluster_two_project_two).id], json_body.map { |n| n['id'] }
  end
end
