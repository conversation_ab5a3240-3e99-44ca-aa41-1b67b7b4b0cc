require 'test_helper'

class Api::V1::Assets::CmsAccessControllerTest < ActionController::TestCase
  setup do
    @user = users(:wik<PERSON>)
    project = projects(:one)
    authenticate(@user)
    @asset = CmsAccess.create!(project: project, name: 'budogram',
                               requester: @user,
                               requested_date: Time.zone.now,
                               notes: 'My awesome notes', url: 'budogram',
                               cms_login: 'cbukowski', user: @user)
    @valid_params = {
      project_id: project.id,
      url: 'www.google.com',
      cms_login: 'my_login',
      user_id: @user.id
    }
  end

  test 'asset is created given valid params' do
    assert_difference('CmsAccess.count') do
      post :create, params: { cms_access: @valid_params }, format: :json
    end

    assert_response :created
  end

  test 'validation fails properly on create' do
    post :create, params: { cms_access: @valid_params.merge(user_id: '') }, format: :json
    assert_response :unprocessable_entity
  end

  test 'returns asset' do
    post :show, params: { id: @asset.id }, format: :json
    assert_equal json_body["id"], @asset.id
    assert_response :ok
  end
end
