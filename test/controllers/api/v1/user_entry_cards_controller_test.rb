require 'test_helper'

class Api::V1::UserEntryCardsControllerTest < ActionController::TestCase
  let(:user) { users(:wiktoria) }
  let(:user_entry_card) { user_entry_cards(:wiktoria_card) }
  let(:valid_attributes) do
    {
      card_number: 3,
      starts_on: 1.day.ago.to_date
    }
  end

  setup do
    authenticate(user)
  end

  test 'index returns all user\'s entry cards' do
    get :index, params: { user_id: user }, format: :json

    assert_response :success
    assert_equal user_entry_card.id, json_body.first['id']
  end

  test 'show returns specific user\'s entry card' do
    get :show, params: { user_id: user, id: user_entry_card }, format: :json

    assert_response :success
    assert_equal user_entry_card.id, json_body['id']
  end

  test 'create adds new record' do
    assert_difference -> { user.user_entry_cards.reload.count } do
      post :create, params: { user_id: user, user_entry_card: valid_attributes }, format: :json
    end

    assert_response :created
  end

  test 'create returns 422 in case of validation error' do
    assert_no_difference -> { user.user_entry_cards.reload.count } do
      post :create, params: { user_id: user,
                              user_entry_card: valid_attributes.merge(starts_on: nil) },
                    format: :json
    end

    assert_response :unprocessable_entity
  end

  test 'update modifies a record' do
    date = Time.zone.today
    patch :update, params: { user_id: user, id: user_entry_card,
                             user_entry_card: { starts_on: date, ends_on: date } }, format: :json

    assert_equal date, user_entry_card.reload.starts_on
    assert_response :no_content
  end

  test 'update returns 422 in case of validation error' do
    patch :update, params: { user_id: user, id: user_entry_card,
                             user_entry_card: { starts_on: nil } }, format: :json

    assert_not_nil user_entry_card.reload.starts_on
    assert_response :unprocessable_entity
  end
end
