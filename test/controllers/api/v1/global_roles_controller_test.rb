require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::GlobalRolesControllerTest < ActionController::TestCase
  fixtures :global_roles, :user_global_roles, :users

  setup do
    authenticate(users(:mkalita_user))
  end

  def global_role
    @global_role ||= global_roles(:global_admin)
    # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
    # HOWTO: test specific version
    # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
    # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
    # or
    # post '/humans',
    #      { human: { name: 'John', brain_type: 'small' } }.to_json,
    #      { 'Accept' => 'application/vnd.api+json; version=2',
    #        'Content-Type' => 'application/vnd.api+json; version=2' }
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::GlobalRolesController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  def test_index_with_param_collection_for_select
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:global_roles).size
    assert !json_body.include?('created_at')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:global_roles).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_page_2
    user_on_first_page = GlobalRole.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_collection_for_select
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:global_roles).size
    assert !json_body.include?('redmine_id')
    assert_kind_of Array, json_body.last['potential_permissions']
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_created_after_filter
    get :index, format: :json, params: { f: { created_after: Time.zone.today } }

    assert_response :success
    assert_equal 10, json_body.count
  end

  def test_index_with_created_before_filter
    get :index, format: :json, params: { f: { created_before: Time.zone.today } }

    assert_response :success
    assert_equal 10, json_body.count
  end

  def test_index_with_term_filter
    get :index, format: :json, params: { f: { term: 'System Administrator' } }

    assert_response :success
    assert_equal 1, json_body.count
    assert_equal global_roles(:global_admin).name, json_body.first['name']
  end

  def test_create
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('GlobalRole.count') do
      post :create, params: { global_role: {
        name: 'Global test create'
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_equal global_role_url(GlobalRole.find(json_body['id'])), response.location
  end

  def test_show
    get :show, params: { id: global_role }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
  end

  def test_update
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    put :update, params: { id: global_role, global_role: {
      name: global_role.name + ' 123'
    } }, format: :json
    assert_response 204, @response.body.to_s
  end

  def test_destroy
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('GlobalRole.count', -1) do
      delete :destroy, format: :json,
                       params: { id: global_roles(:global_role_without_activities_and_associations).id }
      assert_response 204, @response.body.to_s
    end
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    GlobalRolePolicy.stubs(:new).with(user, GlobalRole).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    global_role = global_roles(:global_admin)
    policy = stub(show?: false)
    GlobalRolePolicy.stubs(:new).with(user, global_role).returns(policy)
    sign_in(user)
    get :show, params: { id: global_role.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    policy = stub(create?: false)
    GlobalRolePolicy.stubs(:new).with(user, GlobalRole).returns(policy)
    sign_in(user)
    post :create, params: { global_role: { name: "Rola1" } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    user = users(:mkalita_user)
    global_role = global_roles(:global_admin)
    policy = stub(update?: false)
    GlobalRolePolicy.stubs(:new).with(user, global_role).returns(policy)
    sign_in(user)
    patch :update, params: { id: global_role.id, global_role: { name: "Dev1" } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_destroy_authorization
    user = users(:mkalita_user)
    global_role = global_roles(:global_admin)
    policy = stub(destroy?: false)
    GlobalRolePolicy.stubs(:new).with(user, global_role).returns(policy)
    sign_in(user)
    delete :destroy, params: { id: global_role.id }, format: :json
    assert_response 403, @response.body.to_s
  end
end
