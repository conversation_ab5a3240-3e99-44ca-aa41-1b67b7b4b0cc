require 'test_helper'

class Api::V1::DefaultAgreementContentsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  test 'GET #index' do
    get :index, format: :json
    assert_response :ok, @response.body.to_s
    assert_equal assigns(:default_agreement_contents).size, json_body.size
  end

  test 'PUT #update' do
    default_agreement_content = default_agreement_contents(:one)
    put :update, format: :json, params: { id: default_agreement_content,
                 default_agreement_contents: { company:  companies(:one),
                                               business_to_business: false, content: 'new content' } }
    assert_response 204, @response.body.to_s
    assert_equal 'new content', default_agreement_content.reload.content
  end
end
