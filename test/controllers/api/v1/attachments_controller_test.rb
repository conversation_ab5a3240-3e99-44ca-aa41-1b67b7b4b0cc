require 'test_helper'

class Api::V1::AttachmentsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  test 'POST #create for agreement' do
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    @attachment_count = Attachment.count
    post :create, params: { attachable: agreements(:one),
                            file: Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'),
                                                               'image/jpg') }
    assert_response :created, @response.body.to_s
    assert_equal @attachment_count + 1 , Attachment.count
    attachment = Attachment.last
    assert_equal attachment.file_url, json_body['location']
    assert_equal attachment.id, json_body['id']
  end

  test 'POST #create for project aggreement' do
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    @attachment_count = Attachment.count
    post :create, params: { file: Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'),
                                                               'image/jpg'),
                            attachable: project_agreements(:one) }
    assert_response :created, @response.body.to_s
    assert_equal @attachment_count + 1 , Attachment.count
    attachment = Attachment.last
    assert_equal attachment.file_url, json_body['location']
    assert_equal attachment.id, json_body['id']
  end

  test 'DELETE #bulk_destroy' do
    attachment = attachments(:one)
    assert_difference('Attachment.count', -1) do
      delete :bulk_destroy, params: { ids: [attachment.id] }, format: :json
      assert_response 204, @response.body.to_s
    end
  end

  test 'GET #file' do
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    file = Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'),
                                        'image/jpg')
    attachment = Attachment.new(attachable: agreements(:one), file: file)
    attachment.save
    get :file, format: :json, params: { id: agreements(:one).attachments.first }
    assert_response :success
    assert_equal @response.headers['Content-Transfer-Encoding'], 'binary'
    assert_match "inline; filename=\"#{attachment.file.metadata['filename']}\"",
                 @response.headers['Content-Disposition']
  end

end
