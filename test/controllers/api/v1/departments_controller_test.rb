require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_department_ids"
class Api::V1::DepartmentsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :departments

  def department
    @department ||= departments(:mkalita_department)
    # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
    # HOWTO: test specific version
    # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
    # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
    # or
    # post '/humans',
    #      { human: { name: 'John', brain_type: 'small' } }.to_json,
    #      { 'Accept' => 'application/vnd.api+json; version=2',
    #        'Content-Type' => 'application/vnd.api+json; version=2' }
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::DepartmentsController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  def test_index_with_param_collection_for_select
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:departments).size
    assert_not_includes json_body, 'created_at'
    assert_not_includes json_body, 'chief_name'
    assert_not_includes json_body.pluck('id'), departments(:locked).id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:departments).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_page_2
    user_on_first_page = Department.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'should get index with filters' do
    get :index, format: :json, params: { f: { term: 'a' }, per_page: 1 }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, 1
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'index with multiword term' do
    department = departments(:three)

    get :index, format: :json, params: { f: { term: 'three mkalita_company' } }

    assert_response :success
    assert_equal 1, json_body.size
    assert_equal department.id, json_body.first['id']
  end

  test 'index with created_after filter' do
    authenticate(users(:wiktoria))
    department = departments(:three)
    department.update_column(:created_at, 1.month.from_now)

    get :index, format: :json, params: { f: { created_after: 2.weeks.from_now.to_date } }

    assert_response :success
    assert_equal 1, json_body.size
    assert_equal department.id, json_body.first['id']
  end

  test 'index with created_before filter' do
    authenticate(users(:wiktoria))
    department = departments(:three)
    department.update_column(:created_at, 1.month.ago)

    get :index, format: :json, params: { f: { created_before: 2.weeks.ago.to_date } }

    assert_response :success
    assert_equal 1, json_body.size
    assert_equal department.id, json_body.first['id']
  end

  test 'index with as_chief_or_uber_chief filter' do
    @request.headers['X-Swagger-Sign-In-As'] = users(:mikolaj).id.to_s

    get :index, format: :json, params: { f: { collection_for_select: true, as_chief_or_uber_chief: true } }

    assert_response :success
    assert_equal 2, json_body.size
    assert_same_elements [departments(:three), departments(:mkalita_department)].pluck(:id), json_body.pluck('id')
  end

  test 'index for regular user should work as well' do
    user = users(:mkalita_user)
    user.global_roles = [global_roles(:global_user)]
    user.save

    get :index, format: :json

    assert_response :success
  end

  def test_create
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('Department.count') do
      post :create, params: { department: {
        name: department.name + '_test_create',
        chief_id: department.chief_id,
        company_id: department.company_id,
        mpk_number_id: mpk_numbers(:other).id
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_equal department_url(Department.find(json_body['id'])), response.location
  end

  def test_show
    get :show, params: { id: department }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
  end

  def test_update
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    put :update, params: { id: department, department: {
      name: "#{department.name}_chaged",
      time_reports_not_required: true
    } }, format: :json

    assert_response 204, @response.body.to_s

    assert_equal "#{department.name}_chaged", department.reload.name
    assert_equal true, department.reload.time_reports_not_required
  end

  def test_destroy
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('Department.count', -1) do
      delete :destroy, format: :json, params: { id: department }
      assert_response 204, @response.body.to_s
    end
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    DepartmentPolicy.stubs(:new).with(user, Department).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    department = departments(:mkalita_department)
    policy = stub(show?: false)
    DepartmentPolicy.stubs(:new).with(user, department).returns(policy)
    sign_in(user)
    get :show, params: { id: department.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    policy = stub(create?: false)
    DepartmentPolicy.stubs(:new).with(user, Department).returns(policy)
    sign_in(user)
    post :create, params: { department: { name: 'Department1',
                                          chief_id: users(:mkalita_user_alternative) } },
                  format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    user = users(:mkalita_user)
    department = departments(:mkalita_department)
    policy = stub(update?: false)
    DepartmentPolicy.stubs(:new).with(user, department).returns(policy)
    sign_in(user)
    patch :update, params: { id: department.id, department: { name: 'Department2' } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_destroy_authorization
    user = users(:mkalita_user)
    department = departments(:mkalita_department)
    policy = stub(destroy?: false)
    DepartmentPolicy.stubs(:new).with(user, department).returns(policy)
    sign_in(user)
    delete :destroy, params: { id: department.id }, format: :json
    assert_response 403, @response.body.to_s
  end
end
