require 'test_helper'

class Api::V1::PublicKeysControllerTest < ActionController::TestCase
  setup do
    @user = users(:wik<PERSON>)
    authenticate(@user)
    @valid_params = {
      identifier: 'home_key',
      key: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDDQiJ8ShMS7WGPXcQdau/874l5Dg+CfBkt50mny0QDod0KRWjvfcvs8b/WpfJujSTyFp1As42rNxI4L8S0aYF77Muu7sILi05jzI1c6UCtoIZYLTKkU3kXWP06iStSFb8WZgV0oaoMjCqOUCzCoBG9JEzVwU7VQWzLvUrtyZP+wMsWxGxilUzlrD77gEcfSmmVhwjwc+Zk2DoIwMbDzRpvoxL71MUvV9InJy8IcmjtxRkr4e/BK3m/G4bhtz/kThI+UZkPowhIXZnRiTNF/pg+3kiHIjRdGsu2zgc3MIdKZd8AB9JC4669ALiDYOpi4JttSI+d02va7gYCJJi3Fsgb <EMAIL>',
    }
  end

  test 'index' do
    get :index, format: :json
    assert_response :success
    assert_equal @user.public_keys.count, json_body.size
    assert_includes json_body.first, 'key'
    assert_includes json_body.first, 'identifier'
    assert_includes json_body.first, 'fingerprint'
  end

  test 'create_with_valid_params' do
    assert_difference('PublicKey.count', 1) do
      post :create, params: { public_key: @valid_params }, format: :json
    end
    assert_equal 'home_key', json_body['identifier']
  end

  test 'create_with_invalid_params' do
    params = { identifier: 'home_key', key: '' }
    assert_no_difference('PublicKey.count') do
      post :create, params: { public_key: params }, format: :json
    end
    assert_response :unprocessable_entity
  end

  test 'try_to_create_public_key_for_other_user_as_regular_user' do
    post :create, params: { user_id: users(:milosz).id, public_key: @params }, format: :json
    public_key = PublicKey.last
    assert_equal(users(:wiktoria), public_key.user)
  end

  test 'create public key for other user as an admin' do
    admin = users(:mkalita_global_admin_programmer)
    authenticate(admin)
    assert_difference('PublicKey.count') do
      post :create, params: { user_id: @user.id, public_key: @valid_params }, format: :json
    end
    key = PublicKey.last
    assert_equal @user, key.user
  end

  test 'destroy' do
    assert_difference('PublicKey.count', -1) do
      delete :destroy, params: { id: public_keys(:wiktoria_key).id }, format: :json
    end
    assert_response :success
  end
end
