require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::ProjectAgreementsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :global_roles, :user_global_roles, :users

  def test_index_authorization
    user = users(:mkalita_user) # role doesn't matter
    policy = stub(index?: false)
    ProjectAgreementPolicy.stubs(:new).with(user, ProjectAgreement).returns(policy)
    sign_in(user)
    get :index, params: { project_id: projects(:five).id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_index
    get :index, format: :json, params: { project_id: projects(:one).id }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal json_body.count, 1
    assert_equal json_body.last['project_id'], projects(:one).id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'test selected' do
    project_agreement1 = ProjectAgreement.create!(project: projects(:five), company: companies(:one), business_to_business: true, content: 'project_agreement_1')
    project_agreement2 = ProjectAgreement.create!(project: projects(:five), company: companies(:two), business_to_business: true, content: 'project_agreement_2')
    project_agreement3 = ProjectAgreement.create!(project: projects(:five), company: companies(:one), business_to_business: false, content: 'project_agreement_3')
    project_agreement4 = ProjectAgreement.create!(project: projects(:five), company: companies(:two), business_to_business: false, content: 'project_agreement_4')
    project_agreement5 = ProjectAgreement.create!(project: projects(:five), company: companies(:one), business_to_business: true, content: 'project_agreement_5')
    project_agreement6 = ProjectAgreement.create!(project: projects(:five), company: companies(:two), business_to_business: false, content: 'project_agreement_6')
    get :selected, params: { project_id: projects(:five).id }, format: :json
    assert_response :success, @response.body.to_s
    assert_equal ['project_agreement_2', 'project_agreement_3', 'project_agreement_5', 'project_agreement_6'],  json_body.to_a.map {|e| e['content']}
  end

  test 'updates project agreement' do
    project_agreement = project_agreements(:one)
    put :update, params: { project_id: projects(:one).id,
                           id: project_agreements(:one).id,
                           project_agreement: { content: 'bbb',
                                                attachment_ids: [attachments(:two).id] } }
    assert_response :no_content
  end

  test 'shows project agreement with project_id' do
    get :show, params: { project_id: projects(:one).id, id: project_agreements(:one).id },
               format: :json
    assert_response :success, @response.body.to_s
    assert_equal projects(:one).id, json_body['project_id']
    assert_equal projects(:one).name, json_body['project_name']
    assert_equal project_agreements(:one).company_id, json_body['company_id']
    assert_equal project_agreements(:one).business_to_business, json_body['business_to_business']
    assert_equal project_agreements(:one).content, json_body['content']
    assert_equal project_agreements(:one).attachments.map(&:id), json_body['attachment_ids']
  end

  test 'shows project agreement without project_id' do
    get :show, params: { id: project_agreements(:one).id }, format: :json
    assert_response :success, @response.body.to_s
    assert_equal projects(:one).id, json_body['project_id']
    assert_equal projects(:one).name, json_body['project_name']
    assert_equal project_agreements(:one).company_id, json_body['company_id']
    assert_equal project_agreements(:one).business_to_business, json_body['business_to_business']
    assert_equal project_agreements(:one).content, json_body['content']
    assert_equal project_agreements(:one).attachments.map(&:id), json_body['attachment_ids']
  end
end
