require 'test_helper'

class Api::V1::AssetsControllerTest < ActionController::TestCase
  before do
    @project = projects(:one)

    @user = users(:wik<PERSON>)
    @pm_role = roles(:pm)
    @pm_user = users(:mikolaj)
    @pm_user.departments_as_chief.update_all(chief_id: @user)
    @pm_user.memberships.first.roles.first.update(pm: true)
    authenticate(@user)
    @non_super_admin_user = users(:mikolaj)
    GenericAsset.create!(name: 'My awesome asset', requester: @non_super_admin_user, project: @project,
                         requested_date: Time.zone.now, notes: 'Extra note')
  end

  test 'index works properly showing only relevant data' do
    get :index, params: { project_id: @project }, format: :json
    assert_equal @project.assets.count, response.headers['X-Total'].to_i
  end

  test 'assets_requests_index works properly showing only relevant ' do
    authenticate(@pm_user)
    get :assets_requests_index, format: :json
    assert_equal 9, json_body.count
  end

  test 'activate change asset status form pending to active' do
    asset = GenericAsset.last
    assert_equal 'pending', asset.state
    patch :activate, params: { id: asset.id }, format: :json
    asset.reload
    assert asset.activated_date
    assert_equal 'active', asset.state
  end

  test 'failed activate does not set asset activated_date' do
    server = servers(:server_one)
    server.update(state: :in_progress)

    patch :activate, params: { id: server }, format: :json

    assert_response :unprocessable_entity
    assert_nil server.reload.activated_date
  end

  test 'show returns only specific asset' do
    asset = Domain.first
    get :show, params: { id: asset.id }, format: :json
    assert_equal json_body['id'], asset.id
  end

  test 'show works for VPN without a project' do
    vpn = vpns(:vpn_one)
    vpn.update!(user: users(:milosz), project: nil)

    get :show, params: { id: vpn.id }, format: :json

    assert_response :success
    assert_equal '', json_body['project_name']
  end

  test 'show works for GenericAsset without a project' do
    generic_asset = generic_assets(:one)
    generic_asset.update!(project: nil)

    get :show, params: { id: generic_asset.id }, format: :json

    assert_response :success
    assert_equal '', json_body['project_name']
  end

  test 'history returns versions' do
    asset = Domain.first
    asset.update(phone: '123456789', update_comment: 'because')

    get :history, params: { id: asset.id }, format: :json

    assert_equal 1, json_body.count
    first_revision = json_body.first
    assert_equal ['Mystring', '123456789'], first_revision['changeset']['phone']
    assert_equal 'because', first_revision['comment']
  end

  test 'close change asset status form active to closed' do
    asset = GoogleService.first
    asset.update_column(:state, :active)
    assert_equal 'active', asset.reload.state
    patch :close, params: { id: asset.id }, format: :json
    asset.reload
    assert_equal 'closed', asset.state
  end

  test 'close changes admin asset status form active to decommission_pending' do
    asset = Domain.first
    asset.update_column(:state, :active)
    assert_equal 'active', asset.reload.state
    patch :close, params: { id: asset.id, decommission_comment: 'decommission comment' }, format: :json
    asset.reload
    assert_equal 'decommission_pending', asset.state
    assert_equal @user, asset.passed_to_decommission_by
  end

  test 'close without decommission_comment renders errors properly' do
    asset = Domain.first
    asset.update_column(:state, :active)

    patch :close, params: { id: asset.id }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']
  end

  test 'decommission changes admin asset status form decommission_pending to closed' do
    asset = Domain.first
    asset.update_columns(state: :decommission_pending, passed_to_decommission_by_id: @user.id,
                         decommission_comment: 'decommission comment')
    assert_equal 'decommission_pending', asset.reload.state
    authenticate(users(:mkalita_global_admin_programmer))
    patch :decommission, params: { id: asset.id }, format: :json
    asset.reload
    assert_equal 'closed', asset.state
  end

  test 'reject change asset status form pending to rejected' do
    asset = GenericAsset.last
    assert_equal 'pending', asset.state
    patch :reject, params: { id: asset.id }, format: :json
    asset.reload
    assert_equal 'rejected', asset.state
  end

  test 'update works properly update only specify data' do
    asset = GenericAsset.last
    new_project = projects(:two)
    assert_not_equal new_project.id, asset.project_id
    post :update, params: { project_id: new_project.id, id: asset.id }, format: :json
    asset.reload
    assert_equal new_project.id, asset.project_id
  end

  test 'update adds a comment to the version' do
    asset = GenericAsset.last

    patch :update, params: { id: asset.id, notes: 'notes', update_comment: 'because' }, format: :json

    asset.reload
    assert_equal 'because', asset.versions.last.comment
  end

  test 'stats returns per-type assets statistic as well as expire-soon list' do
    scope = AssetPolicy::Scope.new(@pm_user, Server)

    get :stats, format: :json

    assert_equal scope.resolve.active.count, json_body['Server']['active']
    assert_equal scope.resolve.where('assets.expiry_date < ?', Time.zone.today).active.count,
                 json_body['Server']['expired']
    assert_equal [], json_body['Server']['expire_list']
  end

  test 'stats returns asset statistics filtered by company for admin' do
    user = users(:mkalita_global_admin_programmer)
    authenticate(user)
    company_id = companies(:two).id
    scope = AssetPolicy::Scope.new(user, Server.joins(:project)
                                               .where(projects: { company_id: company_id }))

    get :stats, params: { f: { company_id: company_id } }, format: :json

    assert_equal scope.resolve.active.count, json_body['Server']['active']
  end

  test 'stats returns asset statistics filtered by user' do
    scope = AssetPolicy::Scope.new(@user, Server.where(user: @user))

    get :stats, params: { f: { user_id: @user } }, format: :json

    assert_response :success
    assert_equal scope.resolve.active.count, json_body['Server']['active']
  end

  test 'stats returns asset statistics filtered by project' do
    scope = AssetPolicy::Scope.new(@user, Server.where(project: @project))

    get :stats, params: { f: { project_id: @project } }, format: :json

    assert_response :success
    assert_equal scope.resolve.active.count, json_body['Server']['active']
  end

  test 'my_projects_assets' do
    get :my_projects_assets, format: :json
    scope = Asset.not_rejected

    assert_response :success
    assert_equal AssetPolicy::Scope.new(@user, scope).resolve.count, response.headers['X-Total'].to_i
  end

  test 'my_project_assets with the type filter' do
    get :my_projects_assets, format: :json, params: { f: { type: 'Vpn' } }
    scope = Asset.not_rejected.where(type: 'Vpn')

    assert_response :success
    assert_equal AssetPolicy::Scope.new(@user, scope).resolve.count, response.headers['X-Total'].to_i
  end

  describe Api::V1::AssetsController do
    let!(:cms_asset) do
      asset = cms_accesses(:cms_access_one)
      asset.update_column(:state, :pending)
      asset
    end
    let!(:server_asset) do
      asset = servers(:server_one)
      asset.update_column(:state, :active)
      asset
    end
    let!(:domain_asset) do
      asset = domains(:domain_one)
      asset.update_column(:state, :pending)
      asset
    end
    let!(:vpn_asset) do
      asset = vpns(:vpn_one)
      asset.update_column(:state, :in_progress)
      asset
    end
    let!(:ssl_asset) do
      asset = ssl_certificates(:ssl_certificate_one)
      asset.update_column(:state, :in_progress)
      asset
    end

    describe 'GETs #asset_orders' do
      subject do
        get :asset_orders, params: { per_page: 50, page: 1 }, format: :json
        json_body
      end

      context 'when global_admin has access' do
        before do
          user = users(:wiktoria)
          user.global_roles = [global_roles(:global_admin)]
          authenticate(user)
        end

        it do
          result = subject.map { |x| x['id'] }

          expect(result).must_include(vpn_asset.id)
          expect(result).must_include(ssl_asset.id)

          expect(result).wont_include(domain_asset.id)
          expect(result).wont_include(cms_asset.id)
          expect(result).wont_include(server_asset.id)
        end
      end

      context 'when global_asset_manager has access' do
        before do
          user = users(:wiktoria)
          user.global_roles = [global_roles(:global_asset_manager)]
          authenticate(user)
        end

        it do
          result = subject.map { |x| x['id'] }

          expect(result).must_include(vpn_asset.id)
          expect(result).must_include(ssl_asset.id)

          expect(result).wont_include(domain_asset.id)
          expect(result).wont_include(cms_asset.id)
          expect(result).wont_include(server_asset.id)
        end

        it 'sorts assets by type' do
          get :asset_orders, params: { f: { sort: 'users.username desc' } }, format: :json

          assert_response :success
        end
      end

      context 'when global_accounting has no access' do
        before do
          user = users(:wiktoria)
          user.global_roles = [global_roles(:global_accounting)]
          authenticate(user)
        end

        it do
          subject

          expect(response.status).must_equal(403)
        end
      end

      context 'when global_project_manager has no access' do
        before do
          user = users(:wiktoria)
          user.global_roles = [global_roles(:global_pm)]
          authenticate(user)
        end

        it do
          subject

          expect(response.status).must_equal(403)
        end
      end
    end

    describe 'PATCHes #allow_execution' do
      subject do
        patch :allow_execution, params: { id: asset.id }, format: :json
      end

      context 'when global_project_manager allows domain asset' do
        let(:asset) { domains(:domain_one) }

        before do
          user = users(:wiktoria)
          user.global_roles = [global_roles(:global_pm)]
          authenticate(user)
        end

        it do
          subject

          expect(response.status).must_equal(204)
          expect(asset.reload.state).must_equal('in_progress')
        end
      end

      context 'when global_asset_manager allows server asset' do
        let(:asset) do
          asset = servers(:server_one)
          asset.update_column(:state, :pending)
          asset
        end

        before do
          user = users(:wiktoria)
          user.global_roles = [global_roles(:global_asset_manager)]
          authenticate(user)
        end

        it do
          subject

          expect(response.status).must_equal(204)
          expect(asset.reload.state).must_equal('in_progress')
        end
      end

      context 'when global_admin can\'t accepts domain asset' do
        let(:asset) do
          asset = domains(:domain_one)
          asset.update_column(:state, :pending)
          asset
        end

        before do
          user = users(:wiktoria)
          user.global_roles = [global_roles(:global_admin)]
          authenticate(user)
        end

        it do
          subject

          expect(response.status).must_equal(403)
          expect(asset.reload.state).must_equal('pending')
        end
      end
    end
  end
end
