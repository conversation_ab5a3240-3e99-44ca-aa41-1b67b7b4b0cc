require "test_helper"

module Api
  module V1
    class JpkOptionsControllerTest < ActionController::TestCase
      include Minitest::XSwaggerSignInAs

      test ':index returns successful response' do
        get :index, format: :json
        assert_response :success, @response.body.to_s
      end

      test 'shows all GTU options' do
        get :index, format: :json

        assert_includes json_body, 'gtu'
        refute_empty json_body['gtu']
      end

      test 'shows all transaction_code options' do
        get :index, format: :json

        assert_includes json_body, 'transaction_code'
        refute_empty json_body['transaction_code']
      end
    end
  end
end
