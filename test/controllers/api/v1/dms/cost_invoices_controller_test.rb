require 'test_helper'

module Api
  module V1
    module Dms
      class CostInvoicesControllerTest < ActionController::TestCase
        let(:cost_invoice) { cost_invoices(:dms_cost_invoice_project) }
        let(:valid_params) do
          {
            contractor_id: contractors(:non_user_contractor).id,
            company_id: companies(:one).id,
            sell_date: Time.zone.today,
            due_date: Time.zone.today,
            invoice_date: Time.zone.today,
            currency: 'EUR',
            description: 'Example description',
            cost_projects_attributes: [
              { accounting_number_id: accounting_numbers(:one).id,
                department_id: departments(:two).id, amount: 5_000 }
            ],
            number: "#{Time.zone.today.to_fs(:sql)}/1",
            document: Rack::Test::UploadedFile.new('test/fixtures/files/sample.pdf',
                                                   'application/pdf'),
            cost_invoice_positions_attributes: [{
              name: 'Cost position', amount: 1, unit_price: 5_000, tax_rate: 0
            }],
            payment_method: 'cash',
            flow: 'project',
            kind: 'vat',
            cash_payer_id: users(:internal_user).id
          }
        end
        let(:regular_user) { users(:milosz) }
        let(:department_chief) { users(:mikolaj) }
        let(:board_member) { users(:board_member_user) }
        let(:controller) { users(:wiktoria) }
        let(:acceptation_comment) { 'Acceptation comment' }
        let(:pending_cost_invoice) { cost_invoices(:dms_pending_cost_invoice) }

        before do
          authenticate(controller)
          Settings.nbp_api = 'https://api.nbp.pl/api/exchangerates'
        end

        test 'create cost invoice with project flow' do
          authenticate(regular_user)

          assert_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params }, format: :json
          end
          assert_response :created
        end

        test 'create with improper enum value returns 422' do
          authenticate(regular_user)

          post :create, params: { cost_invoice: valid_params.merge(
            payment_method: 'a', flow: 'b',
            cost_invoice_positions_attributes: [{
              name: 'Cost position', amount: 1, unit_price: 5_000, tax_rate: 'c'
            }]
          ) }, format: :json

          assert_response :unprocessable_entity
        end

        test 'create cost invoice and send to controller' do
          authenticate(regular_user)

          assert_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params.merge(send_to_controller: true) },
                          format: :json
          end
          assert_response :created
          cost_invoice = CostInvoice.find(json_body['id'])
          assert cost_invoice.pending_department?
          assert_not_empty cost_invoice.unaccepted_cost_invoice_acceptances
        end

        test 'newly created cost invoice gets auto-accepted by department chief' do
          authenticate(department_chief)
          valid_params[:cost_projects_attributes].first[:department_id] = departments(:three).id
          valid_params[:send_to_controller] = true

          post :create, params: { cost_invoice: valid_params.merge(send_to_controller: true) },
                        format: :json

          assert_response :created
          cost_invoice = CostInvoice.find(json_body['id'])
          assert cost_invoice.pending_department_uber?
        end

        test 'notification is scheduled upon auto-acceptation by department chief' do
          authenticate(department_chief)
          valid_params[:cost_projects_attributes].first[:department_id] = departments(:three).id
          valid_params[:send_to_controller] = true

          assert_difference -> { ::Dms::NewNotifierWorker.jobs.count } do
            post :create, params: { cost_invoice: valid_params.merge(send_to_controller: true) },
                          format: :json
          end
        end

        test 'newly created cost invoice gets auto-accepted all the way up to pending_controller state' do
          post :create, params: { cost_invoice: valid_params.merge(send_to_controller: true) },
                        format: :json

          assert_response :created
          cost_invoice = CostInvoice.find(json_body['id'])
          assert cost_invoice.pending_controller?
        end

        test 'create cost invoice and send to controller when invalid' do
          post :create, params: { cost_invoice: valid_params.merge(
            send_to_controller: true, cost_invoice_positions_attributes: [{
              name: 'Cost position', amount: 1, unit_price: 10_000, tax_rate: '23'
            }]
          ) }, format: :json

          assert_response :unprocessable_entity
          assert_not_empty JSON.parse(response.body)['errors']['total_amount']
        end

        test 'create cost invoice with general flow' do
          authenticate(board_member)

          assert_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params.merge(flow: 'general') },
                          format: :json
          end
          assert_response :created
        end

        test 'unauthorized create cost invoice with general flow' do
          authenticate(regular_user)

          assert_no_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params.merge(flow: 'general') },
                          format: :json
          end
          assert_response :unprocessable_entity
        end

        test 'create cost invoice with simplified flow' do
          authenticate(controller)

          assert_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params.merge(flow: 'simplified') },
                          format: :json
          end
          assert_response :created
        end

        test 'unauthorized create cost invoice with simplified flow' do
          authenticate(board_member)

          assert_no_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params.merge(flow: 'simplified') },
                          format: :json
          end
          assert_response :unprocessable_entity
        end

        test 'create cost invoice with attachments' do
          attachment_ids = [attachments(:one).id, attachments(:two).id]

          assert_difference('CostInvoice.count') do
            post :create, params: { cost_invoice: valid_params.merge(attachment_ids: attachment_ids) }, format: :json
          end
          assert_response :created
          assert_same_elements attachment_ids, CostInvoice.find(json_body['id']).attachment_ids
        end

        test 'new returns tax rates, flows and payment_methods' do
          authenticate(regular_user)

          get :new, format: :json

          assert_response :success
          assert_equal CostInvoicePosition.tax_rates.keys, assigns(:tax_rates)
          assert_equal [:project], assigns(:flows)
          assert_equal ::Dms::CostInvoice.payment_methods.keys, assigns(:payment_methods)
        end

        test 'show' do
          get :show, params: { id: cost_invoice }, format: :json

          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert_equal CostInvoicePosition.tax_rates.keys, assigns(:tax_rates)
        end

        test 'show with correct acceptor - chief' do
          get :show, params: { id: cost_invoice }, format: :json

          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert json_body['cost_invoice']['acceptors_by_step']['pending_department'].any?(cost_invoice.user.department.chief.full_name)
        end

        test 'show with correct acceptor - uber chief' do
          uber_chief = users(:mikolaj)

          department = cost_invoice.user.department
          department.uber_chief = uber_chief
          department.save!

          chief = department.chief
          chief.save!

          Absence.create({ user_id: chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
          Absence.create({ user_id: users(:wiktoria).id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

          get :show, params: { id: cost_invoice }, format: :json

          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)

          assert_includes json_body['cost_invoice']['acceptors_by_step']['pending_department'], "#{uber_chief.full_name} (#{I18n.t('dms.cost_invoices.replacement_chief')})"
        end

        test 'show with correct acceptor - check correct accepted line' do
          department = cost_invoices(:dms_pending_cost_invoice_two).user.department
          department.uber_chief = users(:milosz)
          department.substitute_chief = users(:mikolaj)

          chief = department.chief
          uber_chief = department.uber_chief
          substitute_chief = department.substitute_chief
          department.save!

          get :show, params: { id: cost_invoice }, format: :json

          # chief is present
          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert_includes json_body['cost_invoice']['acceptors_by_step']['pending_department'], "#{chief.full_name}"

          # chief is taking a vacation
          Absence.create({ user_id: chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
          Absence.create({ user_id: users(:mkalita_user).id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
          Absence.create({ user_id: users(:mkalita_global_admin_programmer).id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

          get :show, params: { id: cost_invoice }, format: :json

          # uber_chief is present
          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert_includes json_body['cost_invoice']['acceptors_by_step']['pending_department'], "#{uber_chief.full_name} (#{I18n.t('dms.cost_invoices.replacement_chief')})"

          # uber_chief is taking a vacation
          Absence.create({ user_id: uber_chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

          get :show, params: { id: cost_invoice }, format: :json

          # substitute_chief is present
          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert_includes json_body['cost_invoice']['acceptors_by_step']['pending_department'], "#{substitute_chief.full_name} (#{I18n.t('dms.cost_invoices.replacement_chief')})"
        end

        test 'show with correct acceptor - substitute chief' do
          substitute_chief = users(:mikolaj)
          department = cost_invoice.user.department
          department.substitute_chief = substitute_chief
          department.save!

          chief = department.chief

          Absence.create({ user_id: chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
          Absence.create({ user_id: users(:wiktoria).id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
          Absence.create({ user_id: users(:mkalita_user).id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
          Absence.create({ user_id: users(:mkalita_global_admin_programmer).id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

          get :show, params: { id: cost_invoice }, format: :json

          assert_response :success
          assert_equal cost_invoice, assigns(:cost_invoice)
          assert_includes json_body['cost_invoice']['acceptors_by_step']['pending_department'], "#{substitute_chief.full_name} (#{I18n.t('dms.cost_invoices.replacement_chief')})"
        end

        test 'update' do
          attributes = { number: '2022/03/2' }

          patch :update, params: { id: cost_invoice, cost_invoice: attributes }, format: :json

          assert_response :no_content
          assert_equal '2022/03/2', cost_invoice.reload.number
        end

        test 'update with send to controller auto accepts cost invoice' do
          attributes = { send_to_controller: true }

          patch :update, params: { id: cost_invoice, cost_invoice: attributes }, format: :json

          assert_response :no_content
          assert_equal 1, cost_invoice.cost_invoice_acceptances.where.not(accepted_at: nil).count
        end

        test 'update with send to controller sends notification' do
          attributes = { send_to_controller: true }

          assert_difference -> { ::Dms::NewNotifierWorker.jobs.count } do
            patch :update, params: { id: cost_invoice, cost_invoice: attributes }, format: :json
          end
        end

        test 'update non-draft cost invoice creates snapshot' do
          cost_invoice = cost_invoices(:dms_pending_cost_invoice)
          attributes = { number: '2022/03/2' }

          assert_difference -> { cost_invoice.snapshots.count }, 1 do
            patch :update, params: { id: cost_invoice, cost_invoice: attributes }, format: :json
          end

          snapshot = cost_invoice.snapshots.last
          assert_equal :update, snapshot.metadata[:action]
          assert_nil snapshot.metadata[:comment]
          assert_equal 'pending_controller', snapshot.metadata[:state_was]
        end

        test 'update non-draft cost invoice creates snapshot with user comment' do
          comment = 'Comment for update'
          cost_invoice = cost_invoices(:dms_pending_cost_invoice)
          attributes = { number: '2022/03/2', comment: comment }

          assert_difference -> { cost_invoice.snapshots.count }, 1 do
            patch :update, params: { id: cost_invoice, cost_invoice: attributes }, format: :json
          end

          snapshot = cost_invoice.snapshots.last
          assert_equal :update, snapshot.metadata[:action]
          assert_equal comment, snapshot.metadata[:comment]
          assert_equal 'pending_controller', snapshot.metadata[:state_was]
        end

        test 'index' do
          get :index, format: :json

          assert_response :success
          assert_equal ::Dms::CostInvoice.count, json_body.count
        end

        test 'index with accepted filter' do
          get :index, params: { f: { accepted: true } }, format: :json

          assert_response :success
          assert_equal ::Dms::CostInvoice.accepted.count, json_body.count
        end

        test 'index with department filter' do
          get :index, params: { f: { department_id: departments(:two) } }, format: :json

          assert_response :success
          assert_equal 3, json_body.count
        end

        test 'index with project filter' do
          get :index, params: { f: { accounting_number_id: accounting_numbers(:two).id } },
                      format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with user filter' do
          get :index, params: { f: { user_id: users(:milosz).id } }, format: :json

          assert_response :success
          assert_equal 2, json_body.count
        end

        test 'index with regular user' do
          user = users(:milosz)
          authenticate(user)

          get :index, format: :json

          assert_response :success
          assert_equal 2, json_body.count
        end

        test 'index with company filter' do
          get :index, params: { f: { company_id: companies(:two) } }, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with to_my_accept filter' do
          get :index, params: { f: { to_my_accept: true } }, format: :json

          assert_response :success
          assert_equal 2, json_body.count

          target_ids = Set.new([cost_invoices(:dms_pending_cost_invoice).id,
                                cost_invoices(:dms_pending_cost_invoice_two).id])
          assert_equal target_ids, Set.new(json_body.map { |object| object['id'] })
        end

        test 'index with acceptor_id filter' do
          cost_invoice.send_to_controller!

          get :index, params: { f: { acceptor_id: users(:mkalita_user).id } }, format: :json

          assert_response :success
          assert_equal [cost_invoice.id], json_body.pluck('id')
        end

        test 'index with number filter' do
          number = '2022/03/4'
          get :index, params: { f: { number: number } }, format: :json

          assert_response :success
          assert_equal 2, json_body.count

          assert_equal [number], json_body.map { |object| object['number'] }.uniq
        end

        test 'index with due_date_from filter' do
          get :index, params: { f: { due_date_from: Time.zone.today + 3.weeks } }, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with due_date_to filter' do
          get :index, params: { f: { due_date_to: Time.zone.today + 3.weeks - 1.day } }, format: :json

          assert_response :success
          assert_equal 3, json_body.count
        end

        test 'index with sell_date_from filter' do
          get :index, params: { f: { sell_date_from: Time.zone.today } }, format: :json

          assert_response :success
          assert_equal 3, json_body.count
        end

        test 'index with sell_date_to filter' do
          get :index, params: { f: { sell_date_to: Time.zone.yesterday } }, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with accepted_after filter' do
          get :index, params: { f: { accepted_after: Time.zone.yesterday } }, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with accepted_before filter' do
          get :index, params: { f: { accepted_before: Time.zone.yesterday } }, format: :json

          assert_response :success
          assert_equal 1, json_body.count
        end

        test 'index with total_amount_from filter' do
          get :index, params: { f: { total_amount_from: 10_000 } }, format: :json

          assert_response :success
          assert_equal 2, json_body.count
        end

        test 'index with total_amount_to filter' do
          get :index, params: { f: { total_amount_to: 9_999 } }, format: :json

          assert_response :success
          assert_equal 2, json_body.count
        end

        test 'index returns pending_acceptors info' do
          get :index, format: :json

          assert json_body.first['pending_acceptors']
        end

        test 'index returns gross value info' do
          get :index, format: :json

          assert json_body.first['gross_value']
        end

        test 'index for account manager' do
          cost_invoice.send_to_controller!
          authenticate(users(:mikolaj))

          get :index, format: :json

          assert_response :success
          assert_equal 2, json_body.count
        end

        test 'index with xlsx format' do
          get :index, params: { f: { export_type: 'project' } }, format: :xlsx

          assert_response :success
        end

        test 'accept returns no content' do
          patch :accept, params: { id: pending_cost_invoice, comment: acceptation_comment },
                         format: :json

          assert_response :no_content
        end

        test 'accept creates cost invoice action record' do
          assert_difference -> { pending_cost_invoice.snapshots.count } do
            patch :accept, params: { id: pending_cost_invoice, comment: acceptation_comment },
                           format: :json
          end

          snapshot = pending_cost_invoice.snapshots.last
          assert_equal acceptation_comment, snapshot.metadata[:comment]
          assert_equal :accept, snapshot.metadata[:action]
          assert_equal 'pending_controller', snapshot.metadata[:state_was]
        end

        test 'accept schedules notification' do
          assert_difference -> { ::Dms::NewNotifierWorker.jobs.count } do
            patch :accept, params: { id: pending_cost_invoice, comment: acceptation_comment },
                           format: :json
          end
        end

        test 'accept returns unprocessable entity if cost invoice is invalid' do
          cost_invoice_acceptance = cost_invoice_acceptances(:dms_pending_cost_invoice_acceptance)
          pending_cost_invoice.paid = true
          pending_cost_invoice.save(validate: false)

          assert_no_difference lambda {
            pending_cost_invoice.unaccepted_cost_invoice_acceptances.count
          } do
            patch :accept, params: { id: pending_cost_invoice, comment: acceptation_comment },
                           format: :json
          end

          assert_response :unprocessable_entity
          assert cost_invoice_acceptance.reload
        end

        test 'accept returns unprocessable entity if cost projects have no cost account numbers' do
          pending_cost_invoice.cost_projects.first.update(cost_account_number_id: nil)

          assert_no_difference lambda {
            pending_cost_invoice.unaccepted_cost_invoice_acceptances.count
          } do
            patch :accept, params: { id: pending_cost_invoice, comment: acceptation_comment },
                           format: :json
          end

          assert_response :unprocessable_entity
          assert pending_cost_invoice.reload.pending_controller?
        end

        test 'accept leave cost invoice snapshot even if acceptation is not completed' do
          user = users(:mkalita_user)
          authenticate(user)
          cost_invoice_acceptance = cost_invoice_acceptances(:dms_pending_cost_invoice_acceptance)
          cost_invoice_acceptance.destroy
          pending_cost_invoice.state = :pending_department
          pending_cost_invoice.save(validate: false)
          pending_cost_invoice.cost_projects.first.update(department_id: departments(:one).id)
          pending_cost_invoice.send(:create_department_acceptances)

          assert_difference -> { ActiveSnapshot::Snapshot.count } do
            patch :accept, params: { id: pending_cost_invoice, comment: acceptation_comment },
                           format: :json
          end

          assert_response :success
          assert pending_cost_invoice.reload.pending_department?
        end

        test 'reject returns no_content and marks cost invoice as for correction' do
          patch :reject, params: { id: pending_cost_invoice, comment: acceptation_comment },
                         format: :json

          assert_response :no_content
          assert pending_cost_invoice.reload.for_correction?
        end

        test 'reject removes all cost invoice acceptances' do
          patch :reject, params: { id: pending_cost_invoice }, format: :json

          assert_empty pending_cost_invoice.unaccepted_cost_invoice_acceptances
        end

        test 'reject creates cost invoice action record' do
          assert_difference -> { ActiveSnapshot::Snapshot.count } do
            patch :reject, params: { id: pending_cost_invoice, comment: acceptation_comment },
                           format: :json
          end

          snapshot = pending_cost_invoice.snapshots.last
          assert_equal acceptation_comment, snapshot.metadata[:comment]
          assert_equal :reject, snapshot.metadata[:action]
          assert_equal 'pending_controller', snapshot.metadata[:state_was]
        end

        test 'reject returns unprocessable entity if cost invoice is invalid' do
          cost_invoice_acceptance = cost_invoice_acceptances(:dms_pending_cost_invoice_acceptance)
          pending_cost_invoice.paid = true
          pending_cost_invoice.save(validate: false)

          patch :reject, params: { id: pending_cost_invoice, comment: acceptation_comment },
                         format: :json

          assert_response :unprocessable_entity
          assert cost_invoice_acceptance.reload
        end

        test 'destroy enables destroying draft invoices' do
          cost_invoice = cost_invoices(:dms_cost_invoice_project)

          assert_difference('::Dms::CostInvoice.count', -1) do
            delete :destroy, params: { id: cost_invoice }, format: :json
          end

          assert_response :no_content
          assert cost_invoice.reload.deleted?
        end

        test 'destroy enables destroying pending controller invoices' do
          cost_invoice = cost_invoices(:dms_pending_cost_invoice)

          assert_difference('::Dms::CostInvoice.count', -1) do
            delete :destroy, params: { id: cost_invoice, cost_invoice: { deletion_reason: 'to be deleted' } }, format: :json
          end

          assert_response :no_content
          cost_invoice.reload
          assert cost_invoice.deleted?
          assert_equal 'to be deleted', cost_invoice.deletion_reason
        end

        test 'GET #document works properly' do
          cost_invoice = cost_invoices(:dms_cost_invoice_project)
          cost_invoice.document_data = TestData.document_data.to_json
          cost_invoice.save

          get :document, params: { id: cost_invoice }, format: :json

          assert_response :success
          assert_equal @response.headers['Content-Transfer-Encoding'], 'binary'
          assert_match "inline; filename=\"#{cost_invoice.document.original_filename}\"",
                       @response.headers['Content-Disposition']
        end

        test 'GET #history returns snapshots' do
          # create snapshot:
          post :create, params: { cost_invoice: valid_params.merge(send_to_controller: true) },
                        format: :json
          id = JSON.parse(response.body)['id']

          get :history, params: { id: id }, format: :json

          assert_response :success
          assert_equal 2, JSON.parse(response.body)['history_records'].count

          history_record = JSON.parse(response.body)['history_records'].first
          assert_equal 'create', history_record['action']
          assert_nil history_record['comment']
          assert_equal 'new', history_record['state_was']
        end

        test 'POST #ocr calls ComarchOcr service and returns recognized data' do
          recognized_data = {
            Fields: {
              DocumentNumber: 'FV/1/09/2022',
              DateOfIssue: '2022-09-30',
              DateOfSale: '2022-09-30',
              DueDate: '2022-10-14',
              PaymentForm: 'przelew',
              BankAccountNumber: 'BankAccountNumber'
            },
            IsCorrection: false,
            SellerContractor: {
              TIN: 'TIN',
              CompanyName: 'CompanyName',
              Street: 'Street',
              StreetNumber: 'StreetNumber',
              PostCode: 'PostCode',
              City: 'Warszawa',
              Voivodeship: 'MAZOWIECKIE',
            },
            BuyerContractor: {
              TIN: '**********',
              CompanyName: 'EFIGENCE SPÓŁKA AKCYJNA',
              Street: 'ul. Wołoska',
              StreetNumber: '9 A',
              PostCode: '02-583',
              City: 'Warszawa',
              Voivodeship: 'MAZOWIECKIE',
            },
            Currency: 'PLN',
            ProductItems: [
              {
                Name: 'Usługi dodatkowe',
                Unit: 'szt',
                Count: 1.0,
                NettoUnitPrice: 250.0,
                BruttoUnitPrice: 307.5,
                Netto: 250.0,
                Brutto: 307.5,
                VatRate: 23.0,
                VatAmount: 57.5
              }
            ]
          }

          ComarchOcr.expects(:process_invoice).returns(recognized_data)

          post :ocr, params: { document: valid_params[:document] }, format: :json

          assert_response :success
          result = JSON.parse(response.body)['recognized_data']
          assert_equal recognized_data[:Fields][:DocumentNumber], result['number']
          assert_equal 'transfer', result['payment_method']
          assert_equal recognized_data[:SellerContractor][:TIN], result['seller']['vat_number']
          assert_equal recognized_data[:BuyerContractor][:CompanyName], result['buyer']['name']
          assert_equal recognized_data[:ProductItems].first[:Name], result['cost_invoice_positions_attributes'].first['name']
        end

        test 'PATCH #recall recalls invoice from accepted' do
          cost_invoice = cost_invoices(:dms_accepted_cost_invoice)
          cost_invoice.update_columns(accepted_at: Time.zone.now)

          patch :recall, params: { id: cost_invoice }, format: :json

          assert_response :no_content
          assert cost_invoice.reload.pending_controller?
        end

        test 'POST #perform_daily_worker' do
          scheduled_set_mock = mock
          scheduled_set_mock.expects(:any?).with_block_given.returns(false)
          Sidekiq::ScheduledSet.expects(:new).returns(scheduled_set_mock)
          CostInvoicesDailyWorker.expects(:perform_in).with(10.seconds, 'DMS', Date.current.to_s)

          post :perform_daily_worker

          assert_response :no_content
        end
      end
    end
  end
end
