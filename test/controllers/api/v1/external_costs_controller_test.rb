require 'test_helper'

class Api::V1::ExternalCostsControllerTest < ActionController::TestCase
  let(:project) { projects(:one) }
  let(:valid_params) {
    {
      amount: 250000,
      mpk_number_id: mpk_numbers(:gui),
      contractor_id: contractors(:non_user_contractor),
      cost_date: 1.month.from_now.to_date,
      currency: 'PLN',
      comment: 'My external cost'
    }
  }

  setup do
    authenticate users(:wiktoria)
  end

  test 'index shows 404 for a project with payment schedule disabled' do
    project = projects(:six)

    get :index, params: { project_id: project }, format: :json

    assert_response :not_found
  end

  test 'index' do
    get :index, params: { project_id: project }, format: :json

    assert_response :success
    assert_equal project.external_costs.count, json_body.count
  end

  test 'index works with xlsx format' do
    get :index, params: { project_id: project }, format: :xlsx

    assert_response :success
  end

  test 'index works without project id' do
    get :index, format: :xlsx

    assert_response :success
  end

  test 'index without project id for non-admin user' do
    user = users(:mikolaj)
    authenticate user
    membership = user.memberships.find_by(project: projects(:one))
    membership.roles << roles(:pm)

    get :index, format: :xlsx

    assert_response :success
  end

  test 'index with month filter' do
    time = 1.month.ago
    month = time.month
    year = time.year

    get :index, params: { project_id: project, f: { year_month: "#{year}-#{month}" } }, format: :json

    assert_response :success
    assert_equal project.external_costs.where(
      cost_date: time.beginning_of_month.to_date..time.end_of_month.to_date
    ).count, json_body.count
  end

  test 'index with improper month filter' do
    get :index, params: { project_id: project, f: { year_month: "#{Date.current.year}-0" } }, format: :json

    assert_response :success
    assert_equal project.external_costs.count, json_body.count
  end

  test 'new shows minimum cost date for a controller' do
    authenticate users(:wiktoria)

    get :new, params: { project_id: project }, format: :json

    assert_response :success
    assert_nil json_body['min_cost_date']
  end

  test 'new shows minimum cost date for a regular user' do
    travel_to(Date.new(2024, 5, 14))
    authenticate users(:mikolaj)

    get :new, params: { project_id: project }, format: :json

    assert_response :success
    assert_equal Date.new(2024, 5, 1).to_s, json_body['min_cost_date']
  end

  test 'create works with valid params' do
    assert_difference -> { project.external_costs.count } do
      post :create, params: { project_id: project, external_cost: valid_params }, format: :json
    end
    assert_response :created
   end

  test 'create fails with invalid params' do
    post :create, params: { project_id: project, external_cost: { amount: 0 } }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['amount']
  end

  test 'cannot create without comment' do
    valid_params.delete(:comment)

    post :create, params: { project_id: project, external_cost: valid_params }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['comment']
  end

  test 'edit' do
    travel_to(Date.new(2024, 5, 14))

    get :edit, params: { project_id: project, id: external_costs(:project_one_next_month) }, format: :json

    assert_response :success
    assert_equal external_costs(:project_one_next_month).id, json_body['id']
    assert_nil json_body['min_cost_date']
  end

  test 'update works with valid params' do
    external_cost = external_costs(:project_one_next_month)

    patch :update, params: { project_id: project, id: external_cost, external_cost: { amount: 1000 } }, format: :json

    assert_response :success
    assert_equal 1000, external_cost.reload.amount
  end

  test 'update fails with invalid params' do
    external_cost = external_costs(:project_one_next_month)

    patch :update, params: { project_id: project, id: external_cost, external_cost: { amount: 0 } }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['amount']
  end

  test 'destroy' do
    external_cost = external_costs(:project_one_next_month)

    assert_difference -> { project.external_costs.count }, -1 do
      delete :destroy, params: { project_id: project, id: external_cost }, format: :json
    end

    assert_response :no_content
  end

  test 'contractor is created with valid params and current user as created by' do
    user = users(:wiktoria)
    sign_in user

    post :create, params: { project_id: project, external_cost: valid_params }, format: :json
    assert_response :created

    id = json_body['id']

    get :index, params: { project_id: project }, format: :json
    assert_response :success
    assert_equal json_body.find { |a| a["id"] == id }['created_by'], user.full_name
  end
end
