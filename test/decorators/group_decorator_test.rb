require 'test_helper'

class GroupDecoratorTest < Draper::TestCase
  fixtures :groups

  def test_group
    assert groups(:mkalita_group).respond_to?(:decorate)
    assert_equal 'GroupDecorator', groups(:mkalita_group).decorate.class.name
    assert_instance_of Array, groups(:mkalita_group).decorate.class.attribute_names_visible_to_all
    # assert_raise(NotImplementedError) do
    #   groups(:mkalita_group).decorate.class.attribute_names_for_collection_for_select_visible_to_all
    # end
    assert_instance_of Array, groups(:mkalita_group).decorate.class.attribute_names_for_collection_for_select_visible_to_all
  end
end
