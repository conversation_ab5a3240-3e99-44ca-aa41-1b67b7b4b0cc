require 'test_helper'

class MpkNumberDecoratorTest < Draper::TestCase
  fixtures :mpk_numbers

  test 'full_name' do
    mpk_number = mpk_numbers(:other).decorate
    assert_equal '119 - Other', mpk_number.full_name
  end

  test 'attributes visible to all' do
    assert mpk_numbers(:motion_design).respond_to?(:decorate)
    assert_equal 'MpkNumberDecorator', mpk_numbers(:motion_design).decorate.class.name
    assert_instance_of Array, mpk_numbers(:motion_design).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id full_name], mpk_numbers(:motion_design).decorate.class.attribute_names_visible_to_all
  end
end
