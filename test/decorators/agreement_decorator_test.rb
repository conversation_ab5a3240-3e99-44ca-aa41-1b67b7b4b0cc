require 'test_helper'

class AgreementDecoratorTest < Draper::TestCase
  fixtures :agreements, :mpk_numbers

  describe '.decorate' do
    setup do
      company = Company.create!(domain: 'xxxx', name: 'xxxx')
      chief = User.create!(email: '<EMAIL>',
                           username: 'username',
                           first_name: 'first',
                           last_name: 'last',
                           activates_on: Time.zone.today,
                           company: company)
      department = Department.create!(
        name: 'department', chief: chief, mpk_number: mpk_numbers(:other)
      )
      @agreement = Agreement.create!(name: 'name',
                                    content: 'Content',
                                    departments: [department],
                                    companies: [company],
                                    business_to_business: true,
                                    confirmation_button_text: 'button text')
    end

    it 'returns decorated agreement' do
      assert @agreement.respond_to?(:decorate)
      assert_equal 'AgreementDecorator', @agreement.decorate.class.name
      assert_instance_of Array, @agreement.decorate.class.attribute_names_visible_to_all
      assert_instance_of Array, @agreement.decorate.class.attribute_names_for_collection_for_select_visible_to_all
    end
  end
end
