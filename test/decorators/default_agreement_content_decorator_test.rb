require 'test_helper'

class DefaultAgreementContentDecoratorTest < Draper::TestCase
  test 'attributes visible to all' do
    assert default_agreement_contents(:one).respond_to?(:decorate)
    assert_equal 'DefaultAgreementContentDecorator', default_agreement_contents(:one).decorate.class.name
    assert_instance_of Array, default_agreement_contents(:one).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id company_id business_to_business content], default_agreement_contents(:one).decorate.class.attribute_names_visible_to_all
  end
end
