require 'test_helper'

class ApprovalDecoratorTest < Draper::TestCase
  test 'attributes visible to all' do
    assert approvals(:milosz_approval).respond_to?(:decorate)
    assert_equal 'ApprovalDecorator', approvals(:milosz_approval).decorate.class.name
    assert_instance_of Array, approvals(:milosz_approval).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id user_id approvable_id accepted approvable_type approvable_name], approvals(:milosz_approval).decorate.class.attribute_names_visible_to_all
  end
end
