require 'test_helper'

class NotificationDecoratorTest < Draper::TestCase
  fixtures :notifications

  def test_notifications
    assert notifications(:mkalita_notification).respond_to?(:decorate)
    assert_equal 'NotificationDecorator', notifications(:mkalita_notification).decorate.class.name
    assert_instance_of Array, notifications(:mkalita_notification).decorate.class.attribute_names_visible_to_all
    assert_instance_of Array, notifications(:mkalita_notification).decorate.class.attribute_names_for_collection_for_select_visible_to_all
  end
end
