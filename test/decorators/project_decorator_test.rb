require 'test_helper'

class ProjectDecoratorTest < Draper::TestCase
  fixtures :projects

  def test_projects
    assert projects(:mkalita_project).respond_to?(:decorate)
    assert_equal 'ProjectDecorator', projects(:mkalita_project).decorate.class.name
    assert_instance_of Array, projects(:mkalita_project).decorate.class.attribute_names_visible_to_all
    assert_instance_of Array, projects(:mkalita_project).decorate.class.attribute_names_for_collection_for_select_visible_to_all
    assert projects(:mkalita_project).decorate.respond_to?(:company_name)
    assert projects(:mkalita_project).decorate.respond_to?(:parent_name)
    assert projects(:mkalita_project).decorate.respond_to?(:subprojects)
  end

  def test_subprojects
    assert_equal projects(:two).decorate.subprojects.count, projects(:two).children.count
    assert_equal projects(:two).decorate.subprojects.first.keys,
                 [:id, :name, :identifier, :state, :account_number]
  end
end
