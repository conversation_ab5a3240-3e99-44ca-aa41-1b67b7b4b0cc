require 'test_helper'

class PublicKeyDecoratorTest < Draper::TestCase
  fixtures :public_keys

  test 'public keys decorator' do
    public_key = public_keys(:wiktoria_key).decorate.as_json
    assert_includes public_key.keys, 'id'
    assert_includes public_key.keys, 'fingerprint'
    assert_includes public_key.keys, 'created_at'
    assert_includes public_key.keys, 'updated_at'
    assert_includes public_key.keys, 'key'
    refute_includes public_key.keys, 'redmine_id'
  end
end
