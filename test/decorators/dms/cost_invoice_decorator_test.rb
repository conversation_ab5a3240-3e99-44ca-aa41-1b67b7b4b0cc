require 'test_helper'

module Dms
  class CostInvoiceDecoratorTest < Draper::TestCase
    let(:pending_cost_invoice) { cost_invoices(:dms_pending_cost_invoice) }
    let(:accepted_cost_invoice) { cost_invoices(:dms_accepted_cost_invoice) }

    setup do
      departments(:two).update(chief: users(:wilhelm))
    end

    test 'expired returns false for accepted cost invoice' do
      assert_not accepted_cost_invoice.decorate.expired
    end

    test 'expired returns false for *fresh* pending cost invoice' do
      assert_not pending_cost_invoice.decorate.expired
    end

    test 'expired returns true for pending cost invoice older than 2 days' do
      pending_cost_invoice.update_column(:pending_department_at, 2.days.ago - 1.minute)

      assert pending_cost_invoice.decorate.expired
    end

    describe 'acceptors_by_step' do
      let(:cost_invoice) { cost_invoices(:dms_cost_invoice_project) }
      let(:accepting_department_user1) { users(:wilhelm) }
      let(:accepting_department_user2) { users(:mkalita_user) }
      let(:accepting_uber_user1) { users(:wiktoria) }
      let(:accepting_uber_user2) { users(:mikolaj) }
      let(:controller_names) do
        User.joins(:global_roles)
            .where(global_roles: { notify_dms_controller_acceptances: true })
            .distinct.map(&:full_name)
      end

      setup do
        cost_invoice.send_to_controller!
      end

      test 'works properly for pending_department cost invoice' do
        assert_equal(
          Set.new([accepting_department_user1.full_name, accepting_department_user2.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department])
        )
        assert_equal(
          Set.new([accepting_uber_user1.full_name, accepting_uber_user2.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department_uber])
        )
        assert_equal(
          Set.new(controller_names),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_controller])
        )
      end

      test 'works properly for chief on holiday' do
        travel_to(Time.zone.local(2023, 12, 7, 12, 0, 0))
        user = users(:wilhelm)
        uber_chief = users(:wiktoria)
        HolidayRequest.create!(applicant: user, starts_on: Date.current, ends_on: Date.current,
                               created_by_user_id: uber_chief.id, updated_by_user_id: uber_chief.id,
                               modified_by_user_at: Time.current)
        HolidayRequest.create!(applicant: accepting_department_user2, starts_on: Date.current, ends_on: Date.current,
                               created_by_user_id: uber_chief.id, updated_by_user_id: uber_chief.id,
                               modified_by_user_at: Time.current)

        assert_equal(
          Set.new(["#{uber_chief.full_name} (#{I18n.t('dms.cost_invoices.replacement_chief')})", "#{accepting_uber_user2.full_name} (#{I18n.t('dms.cost_invoices.replacement_chief')})",
                   accepting_department_user2.full_name, accepting_department_user1.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department])
        )
      end

      test 'works properly for chief and uber on holiday' do
        travel_to(Time.zone.local(2023, 12, 7, 12, 0, 0))
        user = users(:wilhelm)
        uber_chief = users(:wiktoria)
        supervisor = accepting_uber_user2
        department = cost_invoice.user.department
        department.supervisor = supervisor
        department.save!

        HolidayRequest.create!(applicant: user, starts_on: Date.current, ends_on: Date.current,
                               created_by_user_id: uber_chief.id, updated_by_user_id: uber_chief.id,
                               modified_by_user_at: Time.current)
        HolidayRequest.create!(applicant: uber_chief, starts_on: Date.current, ends_on: Date.current,
                               created_by_user_id: uber_chief.id, updated_by_user_id: uber_chief.id,
                               modified_by_user_at: Time.current)
        HolidayRequest.create!(applicant: accepting_department_user2, starts_on: Date.current, ends_on: Date.current,
                               created_by_user_id: uber_chief.id, updated_by_user_id: uber_chief.id,
                               modified_by_user_at: Time.current)

        assert_equal(
          Set.new(["#{supervisor.full_name} (#{I18n.t('dms.cost_invoices.replacement_chief')})",
                   accepting_department_user2.full_name, accepting_department_user1.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department])
        )
      end

      test 'works properly for pending_department_uber cost invoice' do
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_department_user1, cost_invoice: cost_invoice.reload
        )
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_department_user2, cost_invoice: cost_invoice.reload
        )

        assert_equal(
          Set.new([accepting_department_user1.full_name, accepting_department_user2.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department])
        )
        assert_equal(
          Set.new([accepting_uber_user1.full_name, accepting_uber_user2.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department_uber])
        )
        assert_equal(
          Set.new(controller_names),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_controller])
        )
      end

      test 'works properly for pending_controller cost invoice' do
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_department_user1, cost_invoice: cost_invoice.reload
        )
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_department_user2, cost_invoice: cost_invoice.reload
        )
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_uber_user2, cost_invoice: cost_invoice.reload
        )

        assert_equal(
          Set.new([accepting_department_user1.full_name, accepting_department_user2.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department])
        )
        assert_equal(
          Set.new([accepting_uber_user2.full_name]),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_department_uber])
        )
        assert_equal(
          Set.new(controller_names),
          Set.new(cost_invoice.decorate.acceptors_by_step[:pending_controller])
        )
      end

      test 'works properly for accepted cost invoice' do
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_department_user2, cost_invoice: cost_invoice.reload
        )
        travel 1.hour
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_department_user1, cost_invoice: cost_invoice.reload
        )
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_uber_user1, cost_invoice: cost_invoice.reload
        )
        Dms::AcceptCostInvoiceInteractor.call(
          user: accepting_uber_user1, cost_invoice: cost_invoice.reload
        )

        assert_equal(
          { pending_department: [accepting_department_user2.full_name,
                                 accepting_department_user1.full_name],
            pending_department_uber: [accepting_uber_user1.full_name],
            pending_controller: [accepting_uber_user1.full_name] },
          cost_invoice.decorate.acceptors_by_step
        )
      end
    end
  end
end
