require 'test_helper'

class PaymentScheduleDecoratorTest < Draper::TestCase
  setup do
    @payment_schedule = payment_schedules(:project_one_schedule).reload.decorate
  end

  test 'target_current_amount returns proper value' do
    assert_equal [{ currency: CURRENCIES[0].to_s, kind: 'invoice', amount: 1500 }], @payment_schedule.target_current_amount
  end

  test 'target_total_amount returns proper value' do
    payments(:two).update_columns(kind: :accounting_note)
    assert_equal [{ currency: CURRENCIES[0].to_s, kind: 'invoice', amount: 1500 },
                  { currency: CURRENCIES[0].to_s, kind: 'accounting_note', amount: 500 }], @payment_schedule.target_total_amount
  end

  test 'current_amount returns proper value' do
    payment_schedule = payment_schedules(:project_two_schedule).decorate
    assert_equal([{ currency: 'PLN', kind: 'invoice', amount: 200 }], payment_schedule.current_amount)
  end
end
