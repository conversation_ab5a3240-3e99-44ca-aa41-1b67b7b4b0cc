require 'test_helper'

class ApiKeyDecoratorTest < Draper::TestCase
  test 'attributes visible to all' do
    assert api_keys(:key).respond_to?(:decorate)
    assert_equal 'ApiKeyDecorator', api_keys(:key).decorate.class.name
    assert_instance_of Array, api_keys(:key).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id name key_prefix expires_on key], api_keys(:key).decorate.class.attribute_names_visible_to_all
  end
end
