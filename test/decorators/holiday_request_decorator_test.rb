require 'test_helper'

class HolidayRequestDecoratorTest < Draper::TestCase
  test 'problems show holiday requests limit for /Ż' do
    user = users(:wiktoria)
    HolidayRequest.where(applicant: user).destroy_all
    user.update(absence_balance: 30)
    date = Time.zone.today.beginning_of_month

    4.times do
      date += 1
      date += 1 until user.working_day?(date)

      HolidayRequest.create!(
        applicant: user,
        created_by_user: user,
        updated_by_user: user,
        modified_by_user_at: Time.zone.now,
        examiner: user,
        category: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Ż',
        visible: true,
        starts_on: date,
        ends_on: date,
        accepted_at: Time.zone.now
      )
    end
    date += 1
    date += 1 until user.working_day?(date)
    holiday_request = HolidayRequest.create!(
      applicant: user,
      created_by_user: user,
      updated_by_user: user,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostę<PERSON><PERSON>ć/Ż',
      starts_on: date,
      ends_on: date
    ).decorate

    assert_includes(
      holiday_request.problems,
      "User can't have more than 4 days of absence from category 'Niedostęp<PERSON>ś<PERSON>/Ż' in a year " \
      "(#{Time.zone.now.year} in this case)."
    )
  end

  test 'problems show when the holiday request is created too late' do
    user = users(:wiktoria)
    HolidayRequest.where(applicant: user).destroy_all
    user.update(absence_balance: 30)

    holiday_request = HolidayRequest.create!(
      applicant: user,
      created_by_user: user,
      updated_by_user: user,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność',
      starts_on: Date.tomorrow,
      ends_on: 1.month.from_now.to_date
    ).decorate

    assert_includes(
      holiday_request.problems,
      'Holiday request for at least 10 days of absence must be submitted at least 90 days ahead.'
    )
  end
end
