require 'test_helper'

class BookingResourceDecoratorTest < Draper::TestCase
  test 'attributes visible to all' do
    assert booking_resources(:big_conference_room).respond_to?(:decorate)
    assert_equal 'BookingResourceDecorator', booking_resources(:big_conference_room).decorate.class.name
    assert_instance_of Array, booking_resources(:big_conference_room).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id name identifier email multiple_bookings kind created_at], booking_resources(:big_conference_room).decorate.class.attribute_names_visible_to_all
  end
end
