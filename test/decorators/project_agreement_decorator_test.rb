require 'test_helper'

class ProjectAgreementDecoratorTest < Draper::TestCase
  fixtures :project_agreements

  test 'attributes visible to all' do
    assert project_agreements(:one).respond_to?(:decorate)
    assert_equal 'ProjectAgreementDecorator', project_agreements(:one).decorate.class.name
    assert_instance_of Array, project_agreements(:one).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id name project_id company_id business_to_business content published_at
                    project_name confirmation_button_text created_at state attachment_ids],
                 project_agreements(:one).decorate.class.attribute_names_visible_to_all
  end

  test 'project_name' do
    project_agreement = project_agreements(:one).decorate
    assert_equal 'MyString', project_agreement.project_name
  end
end
