require 'test_helper'

class PositionDecoratorTest < Draper::TestCase
  test 'attributes visible to all' do
    assert positions(:one).respond_to?(:decorate)
    assert_equal 'PositionDecorator', positions(:one).decorate.class.name
    assert_instance_of Array, positions(:one).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id name], positions(:one).decorate.class.attribute_names_visible_to_all
  end
end
