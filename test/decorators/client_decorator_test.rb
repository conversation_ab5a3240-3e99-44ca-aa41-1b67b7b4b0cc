require 'test_helper'

class ClientDecoratorTest < Draper::TestCase
  setup do
    @client = clients(:arte).decorate
  end

  test 'invoice address from the client' do
    full_invoice_data = @client.full_invoice_data
    assert_equal full_invoice_data[:street], @client.street
    assert_equal full_invoice_data[:street_number], @client.street_number
    assert_equal full_invoice_data[:city], @client.city
    assert_equal full_invoice_data[:postcode], @client.postcode
    assert_equal full_invoice_data[:country], @client.country
    assert_equal full_invoice_data[:name], @client.name
  end
end
