require 'test_helper'

class CompanyDecoratorTest < Draper::TestCase
  fixtures :companies

  def test_company
    assert companies(:mkalita_company).respond_to?(:decorate)
    assert_equal 'CompanyDecorator', companies(:mkalita_company).decorate.class.name
    assert_instance_of Array, companies(:mkalita_company).decorate.class.attribute_names_visible_to_all
    assert_instance_of Array, companies(:mkalita_company).decorate.class.attribute_names_for_collection_for_select_visible_to_all
  end
end
