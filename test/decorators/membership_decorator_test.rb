require 'test_helper'

class MembershipDecoratorTest < Draper::TestCase
  fixtures :memberships

  def test_membership
    assert memberships(:mkalita_membership_user).respond_to?(:decorate)
    assert_equal 'MembershipDecorator', memberships(:mkalita_membership_user).decorate.class.name
    assert_instance_of Array, memberships(:mkalita_membership_user).decorate.class.attribute_names_visible_to_all
    assert_instance_of Array, memberships(:mkalita_membership_user).decorate.class.attribute_names_for_collection_for_select_visible_to_all
  end
end
