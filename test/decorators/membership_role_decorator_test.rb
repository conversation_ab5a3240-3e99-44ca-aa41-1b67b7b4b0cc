require 'test_helper'

class MembershipRoleDecoratorTest < Draper::TestCase
  fixtures :membership_roles

  def test_membership_roles
    assert membership_roles(:one).respond_to?(:decorate)
    assert_equal 'MembershipRoleDecorator', membership_roles(:one).decorate.class.name
    assert_raise(NotImplementedError) do
      membership_roles(:one).decorate.class.attribute_names_visible_to_all
    end
    assert_raise(NotImplementedError) do
      membership_roles(:one).decorate.class.attribute_names_for_collection_for_select_visible_to_all
    end
  end
end
