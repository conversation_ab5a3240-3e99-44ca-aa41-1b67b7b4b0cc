---
http_interactions:
- request:
    method: get
    uri: https://api.clickup.com/api/v2/team/458213548476
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      Authorization:
      - pk_188448912_CEZLYGB9AVK1U5UOM12R1RA1Y5105TIRA
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Date:
      - Tu<PERSON>, 20 May 2025 13:13:56 GMT
      Timing-Allow-Origin:
      - "*"
      X-Received-From:
      - shard-prod-eu-west-1-3
      Server:
      - nginx
      X-Dns-Prefetch-Control:
      - 'off'
      Expect-Ct:
      - max-age=0
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Download-Options:
      - noopen
      X-Content-Type-Options:
      - nosniff
      X-Permitted-Cross-Domain-Policies:
      - none
      X-Xss-Protection:
      - '0'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Credentials:
      - 'true'
      Access-Control-Expose-Headers:
      - x-datadog-trace-id,server
      X-Datadog-Trace-Id:
      - '788682590027398922'
      Content-Security-Policy:
      - frame-ancestors 'self'
      Cache-Control:
      - no-cache, no-store
      Pragma:
      - no-cache
      Expires:
      - '0'
      X-Ratelimit-Limit:
      - '100'
      X-Ratelimit-Remaining:
      - '99'
      X-Ratelimit-Reset:
      - '1747746897'
      Vary:
      - accept-encoding
      X-Cache:
      - Miss from cloudfront
      Via:
      - 1.1 90ecdc7529deb4cf6ecb56c4626e0ac8.cloudfront.net (CloudFront)
      X-Amz-Cf-Pop:
      - WAW51-P1
      Alt-Svc:
      - h3=":443"; ma=86400
      X-Amz-Cf-Id:
      - c6ynlPn6JOQlsT3hLmMvjBA9W5L0qxlN5bFaYB85VX0YFXU63fORDA==
    body:
      encoding: ASCII-8BIT
      string: '{"team":{"id":"458213548476","members":[{"user":{"id":45648321,"email":"<EMAIL>"}}]}}'
  recorded_at: Tue, 20 May 2025 13:13:56 GMT
recorded_with: VCR 6.1.0
