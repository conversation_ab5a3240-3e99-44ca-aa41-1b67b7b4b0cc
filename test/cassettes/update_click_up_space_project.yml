---
http_interactions:
- request:
    method: put
    uri: https://api.clickup.com/api/v2/space/811
    body:
      encoding: UTF-8
      string: '{"name":"Edited project seven"}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      Authorization:
      - pk_188448912_CEZLYGB9AVK1U5UOM12R1RA1Y5105TIRA
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '821'
      Connection:
      - keep-alive
      Date:
      - Tue, 20 May 2025 13:49:16 GMT
      X-Received-From:
      - shard-prod-eu-west-1-3
      Server:
      - nginx
      X-Dns-Prefetch-Control:
      - 'off'
      Expect-Ct:
      - max-age=0
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Download-Options:
      - noopen
      X-Content-Type-Options:
      - nosniff
      X-Permitted-Cross-Domain-Policies:
      - none
      X-Xss-Protection:
      - '0'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Credentials:
      - 'true'
      Access-Control-Expose-Headers:
      - x-datadog-trace-id,server
      X-Datadog-Trace-Id:
      - '8737704071377634815'
      Content-Security-Policy:
      - frame-ancestors 'self'
      Cache-Control:
      - no-cache, no-store
      Pragma:
      - no-cache
      Expires:
      - '0'
      X-Ratelimit-Limit:
      - '100'
      X-Ratelimit-Remaining:
      - '99'
      X-Ratelimit-Reset:
      - '1747749017'
      Timing-Allow-Origin:
      - "*"
      X-Cache:
      - Miss from cloudfront
      Via:
      - 1.1 b159f39ee34c14548a9d9dc3e730676a.cloudfront.net (CloudFront)
      X-Amz-Cf-Pop:
      - VIE50-P1
      Alt-Svc:
      - h3=":443"; ma=86400
      X-Amz-Cf-Id:
      - CVx8h92vkGo61w-1x3xlfge0w7PkpKIbk4AubNU4z0tWdZtVL8l4Fg==
    body:
      encoding: UTF-8
      string: '{ "id" : "90154589397", "name" : "Edited project seven", "color" : null, "private" : false, "avatar" : null, "admin_can_manage" : null, "statuses" : [ { "id" : "p90154589397_hhEvqm8F", "status" : "todo", "type" : "open", "orderindex" : 0, "color" : "#87909e" }, { "id" : "p90154589397_VsmPw0Pe", "status" : "complete", "type" : "closed", "orderindex" : 1, "color" : "#008844" } ], "multiple_assignees" : false, "features" : { "due_dates" : { "enabled" : true, "start_date" : true, "remap_due_dates" : true, "remap_closed_due_date" : false }, "sprints" : { "enabled" : true },
    "time_tracking" : { "enabled" : true, "harvest" : false, "rollup" : true, "default_to_billable" : 2 }, "points" : { "enabled" : true }, "custom_items" : { "enabled" : false }, "priorities" : { "enabled" : true, "priorities" : [ { "color" : "#f50000", "id" : "1", "orderindex" : "1", "priority" : "urgent" }, { "color" : "#f8ae00", "id" : "2", "orderindex" : "2", "priority" : "high" }, { "color" : "#6fddff", "id" : "3", "orderindex" : "3", "priority" : "normal" }, { "color" : "#d8d8d8", "id" : "4", "orderindex" : "4", "priority" : "low" } ] },
    "tags" : { "enabled" : true }, "time_estimates" : { "enabled" : true, "rollup" : true, "per_assignee" : false }, "checklists" : { "enabled" : true }, "check_unresolved" : { "enabled" : true, "subtasks" : null, "checklists" : null, "comments" : null }, "milestones" : { "enabled" : true }, "custom_fields" : { "enabled" : true }, "remap_dependencies" : { "enabled" : true }, "dependency_warning" : { "enabled" : true }, "status_pies" : { "enabled" : true }, "emails" : { "enabled" : true }, "scheduler_enabled" : false}, "archived" : false}'
  recorded_at: Tue, 20 May 2025 13:49:16 GMT
recorded_with: VCR 6.1.0
