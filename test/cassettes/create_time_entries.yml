---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/time_entries.json?f%5B%5D=project_id&f%5B%5D=spent_on&f%5B%5D=user_id&limit=100&op%5Bproject_id%5D==&op%5Bspent_on%5D=%3E%3C&op%5Buser_id%5D==&page=1&v%5Bproject_id%5D%5B0%5D=&v%5Bproject_id%5D%5B1%5D=796&v%5Bspent_on%5D%5B0%5D=2017-01-03&v%5Bspent_on%5D%5B1%5D=2017-01-08&v%5Buser_id%5D%5B0%5D=1155
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"9eb0058958ef047193f2853d31c891ca"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - bf2abb57-0178-4805-ad7f-ec98080c1ffb
      X-Runtime:
      - '0.601037'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"time_entries":[{"id":457837,"project":{"id":1124,"name":"Konica Minolta
        - portal B2B - development"},"issue":{"id":182500},"user":{"id":1155,"name":"Diego
        Mendilaharzu"},"activity":{"id":9,"name":"Development"},"hours":8.0,"comments":".","spent_on":"2017-01-05","created_on":"2019-01-23T11:13:47+01:00","updated_on":"2019-01-23T11:13:47+01:00"},{"id":457836,"project":{"id":1124,"name":"Konica
        Minolta - portal B2B - development"},"issue":{"id":182500},"user":{"id":1155,"name":"Diego
        Mendilaharzu"},"activity":{"id":9,"name":"Development"},"hours":8.0,"comments":".","spent_on":"2017-01-04","created_on":"2019-01-23T11:13:47+01:00","updated_on":"2019-01-23T11:13:47+01:00"}],"total_count":2,"offset":0,"limit":25}'
    http_version:
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: delete
    uri: http://local.non.3dart.com:3001/imperator_api/v1/time_entries/destroy.json
    body:
      encoding: UTF-8
      string: '{"ids":[457837,457836]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json
      Cache-Control:
      - no-cache
      X-Request-Id:
      - bacdafac-966b-45a1-a0bd-2a7f5ac62fa5
      X-Runtime:
      - '0.067986'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
    http_version:
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/time_entries.json
    body:
      encoding: UTF-8
      string: '{"time_entry":{"user_id":1155,"project_id":1124,"issue_id":182500,"spent_on":"2017-01-03","spent_to":"2017-01-08","comments":".","hours":8}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 201
      message: Created
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Location:
      - http://local.non.3dart.com:3001/time_entries/457839
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"0d391e23dbb37af7044b6ed098b86aa2"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - _redmine_session=M0JpK0s4VWk2cStKb2t6WmNQbUNiMlVUU3pPUnJDTTNrU28vQXcxSGJqS1FwUEg3c003VGt6Szd2ellaYzhKM0FML0R3NktTaW9YNEdGMSt1WkU1NlhLYWlia2RkZW9qT3FKYklqbVRlOUF4SlJDYlFBYW9zRnNEOTZtNFNBdUYtLUVDQWNycG5KNktMT2liSjNtKy8wYVE9PQ%3D%3D--889ba7830425558fbd8d0bdf88aaa9183714333a;
        path=/; HttpOnly
      X-Request-Id:
      - 30d96268-8c57-47c1-bf68-68206a7e32cb
      X-Runtime:
      - '0.180678'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"time_entry":{"id":457839,"project":{"id":1124,"name":"Konica Minolta
        - portal B2B - development"},"issue":{"id":182500},"user":{"id":1155,"name":"Diego
        Mendilaharzu"},"activity":{"id":9,"name":"Development"},"hours":8.0,"comments":".","spent_on":"2017-01-03","created_on":"2019-01-23T11:21:37+01:00","updated_on":"2019-01-23T11:21:37+01:00"}}'
    http_version:
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
recorded_with: VCR 3.0.3
