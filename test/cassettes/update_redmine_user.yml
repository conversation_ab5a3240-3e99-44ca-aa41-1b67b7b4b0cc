---
http_interactions:
- request:
    method: put
    uri: http://local.non.3dart.com:3001/imperator_api/v1/users/1155.json
    body:
      encoding: UTF-8
      string: '{"user":{"login":"whanow<PERSON><PERSON>","mail":"whanow<PERSON><PERSON>@artegence.com","firstname":"<PERSON><PERSON><PERSON>","lastname":"<PERSON><PERSON><PERSON><PERSON>","status":1}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache
      Set-Cookie:
      - request_method=PUT; path=/
      X-Request-Id:
      - eee6c079-9c4a-4e27-ac88-a786ff3a3814
      X-Runtime:
      - '0.098705'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Tue, 17 May 2016 12:53:03 GMT
recorded_with: VCR 3.0.1
