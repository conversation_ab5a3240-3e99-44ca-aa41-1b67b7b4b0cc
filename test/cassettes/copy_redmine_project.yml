---
http_interactions:
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/projects/8/copy.json
    body:
      encoding: UTF-8
      string: '{"project":{"name":"Newcopyname","identifier":"newcopyname"}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 201
      message: 'Created '
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"19f585fa1756ac25cba2e0f56cb6dd5b"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 7f6e92fc-dc74-4945-bfc6-0a67c06fec87
      X-Runtime:
      - '0.122064'
      Server:
      - WEBrick/1.3.1 (Ruby/2.3.0/2015-12-25)
      Date:
      - Wed, 22 Jun 2016 08:59:56 GMT
      Content-Length:
      - '258'
      Connection:
      - Keep-Alive
    body:
      encoding: UTF-8
      string: '{"id":240,"name":"Newcopyname","identifier":"newcopyname","description":null,"homepage":"","is_public":true,"parent_id":null,"created_on":"2016-06-22T08:59:56.000Z","updated_on":"2016-06-22T08:59:56.000Z","status":1,"lft":49,"rgt":50,"inherit_members":false}'
    http_version: 
  recorded_at: Wed, 22 Jun 2016 08:59:56 GMT
recorded_with: VCR 3.0.3
