---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/users/1155.json?include=memberships
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"d9e476ca9a8675057f7cf55dedf50ce2"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - a2cbc349-90a0-4e36-95b9-0efc089ca00d
      X-Runtime:
      - '0.085170'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"user":{"id":1155,"login":"whanow<PERSON><PERSON>","firstname":"<PERSON><PERSON><PERSON>","lastname":"<PERSON>owerska","mail":"<EMAIL>","created_on":"2016-05-17T12:33:56Z","api_key":"b21a1178f3506ae7d38f0395a6c06eb3dd773740","status":1,"custom_fields":[{"id":4,"name":"Team
        Stats","value":"2 weeks"}],"memberships":[{"id":71435,"project":{"id":818,"name":"MyStringTwo"},"roles":[{"id":33,"name":"Account"}]}]}}'
    http_version: 
  recorded_at: Tue, 31 May 2016 10:35:43 GMT
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/memberships/71435.json
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"ddb4348ab9ff56380d322c020fce5565"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - bfc23e0a-9109-49ff-bd91-35b28e99423b
      X-Runtime:
      - '0.064763'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"membership":{"id":71435,"project":{"id":818,"name":"MyStringTwo"},"user":{"id":1155,"name":"Wiktoria
        Hanowerska"},"roles":[{"id":33,"name":"Account"}]}}'
    http_version: 
  recorded_at: Tue, 31 May 2016 10:35:43 GMT
- request:
    method: delete
    uri: http://local.non.3dart.com:3001/imperator_api/v1/memberships/71435.json
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache
      Set-Cookie:
      - request_method=DELETE; path=/
      X-Request-Id:
      - d76c6573-7f25-4602-aee8-1f5ac17e7549
      X-Runtime:
      - '0.070638'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Tue, 31 May 2016 10:35:43 GMT
recorded_with: VCR 3.0.1
