---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings.json?date_from=2017-01-03&date_to=2017-01-08&f%5B%5D=assigned_to_id&f%5B%5D=project_id&limit=100&op%5Bassigned_to_id%5D==&op%5Bproject_id%5D==&page=1&v%5Bassigned_to_id%5D%5B0%5D=1155&v%5Bproject_id%5D%5B0%5D=&v%5Bproject_id%5D%5B1%5D=796
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"e54662723a3bd82bbed0ff2ef20a5043"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 71aea01f-92af-459a-93d2-1ba4e1dbef78
      X-Runtime:
      - '0.660773'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"resources":[{"id":24,"assigned_to_id":1155,"project_id":796,"issue_id":169527,"start_date":"2017-01-03T00:00:00+01:00","end_date":"2017-01-08T00:00:00+01:00","hours_per_day":8.0,"notes":".","created_at":"2023-07-17T10:53:54+02:00","updated_at":"2023-07-17T10:53:54+02:00"}],"total_count":1}'
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: delete
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings/destroy.json
    body:
      encoding: UTF-8
      string: '{"ids":[24]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json
      Cache-Control:
      - no-cache
      X-Request-Id:
      - 5f48a17f-b3d0-4b65-9a74-5348e00327de
      X-Runtime:
      - '0.029649'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings.json
    body:
      encoding: UTF-8
      string: '{"resource_booking":{"assigned_to_id":1155,"project_id":796,"issue_id":169527,"start_date":"2017-01-03","end_date":"2017-01-08","notes":".","hours_per_day":8}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 201
      message: Created
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"f96c20eaea98b19da740c4faa965c449"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - _redmine_session=L0U0dEtuZzRGSEFaR1dYTnZoM01adjNpN3JWUEhUZXNzeXp4a0FIdHZocURHc0RONHRaNXU5ZHdLNG5TZk1LUkRVajZhQkhMM0RqejBhdG9ZcENVc3VlMWloL3U2ZUNlREpTWGx6VkdKZ3Aya1VIamtWRjcyanVmSFVBdFVJWlErUEdud3kxRnVIOUYrTlhWOUdvVUZ1UzhvT2dYUFVFWlZkZDZzVnFmb2RXd2RLdUFMdjBYSjlCblZnS2Rmek5EaG1oRHlMVUt2eUFidjdtQTQvRmlmRzYyTk1qVGhoWmkvSFFFMXR2YUluTmJTMmU0UHJSQlU2RzlPNnJ1QlIybGhFUlhTaERXcGdDVjAwWVVBdUJZN3BnTnN3dlU0WHpnOXl5Tlo5bHkvdWtqUzF0T3RTbmFqWW40T09hMnJ5NUZueDZlaWEwTnVqcHpWa0h3MVBnUkFmYU9KQ081VW0veUZyWEdFc3BhZjNBbXJ5NjA5bmZ0azk2ZHlrTzhPb2Q1bjNGR0F6WCt5MFV6QkVNODFPL1FMcjdRaUNLZkFRVk1CY0dsQ0J1Yks3UFk3bGhzVWJyakRUM1A3aWxyNVBEOGxCQU41bDdpcks0TnRmd1dVdCszOTF1SWY1NmtiY1VoajRnMVhTc0czcVVSbUI4cFJCTHM2Nkdzb2c4SjdCNVhLRENpTmdlcTJCNHprMHg1Um1iaThPZ20reWJvVkVEby84Y3Rya0dIUlVrPS0tVkg5U3JlUVNaM3B0UElTYWJmL0N5UT09--edf490871f2f9f15979d81fdcad55947a677a98f;
        path=/; HttpOnly
      X-Request-Id:
      - 6d91004a-fb4f-482d-b520-eaa02d858317
      X-Runtime:
      - '0.678706'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"resource":{"id":25,"assigned_to_id":1155,"project_id":796,"issue_id":169527,"start_date":"2017-01-03T00:00:00+01:00","end_date":"2017-01-08T00:00:00+01:00","hours_per_day":8.0,"notes":".","created_at":"2023-07-17T11:01:02+02:00","updated_at":"2023-07-17T11:01:02+02:00"}}'
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
recorded_with: VCR 6.1.0
