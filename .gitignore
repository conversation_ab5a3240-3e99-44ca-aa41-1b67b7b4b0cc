# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
!/log/.keep
/tmp
/.rsync_cache
/.front_rsync_cache

config/database.yml
/vendor/bundle
config/deploy/personal_production.local.rb
coverage
.byebug_history
config/settings.local.yml
config/settings/*.local.yml
config/environments/*.local.yml
.env.local
.ruby-version
rails_best_practices_output.html
out.txt
unindexed_foreign_keys.txt
config/app.yml.bk
.nvmrc

db/uploads/*
!db/uploads/test
db/uploads/test/*
!db/uploads/test/estelligence
db/uploads/test/estelligence/*
!db/uploads/test/estelligence/Efigence
db/uploads/test/estelligence/Efigence/*
!db/uploads/test/estelligence/Efigence/KSIĘGOWOŚĆ
db/uploads/test/estelligence/Efigence/KSIĘGOWOŚĆ/*
!db/uploads/test/estelligence/Efigence/KSIĘGOWOŚĆ/sprzedaż

public/uploads/

# RubyMine

/.idea
