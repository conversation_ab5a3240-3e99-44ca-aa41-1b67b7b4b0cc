## Pobieranie listy klastrów

```
GET /api/kubernetes_clusters
```

## Pobieranie listy namespaces w klastrze

```
GET /api/assets/kubernetes_namespaces.json?cluster_name=cluster_name
```

## Po nazwie projketu lookup jakie klastry i namespace ma przypisane + pełne info o namespace

```
GET /api/assets/kubernetes_namespaces.json?project_identifier=project_identifier
```

## Po klaster + namespace pełne info na jego temat (owner, czas dodania/zamknięcia, stan etc)

```
GET /api/assets/kubernetes_namespaces.json?cluster_name=cluster_name&name=name
```

## Po szczegóły klastra (razem z listą użytkowników) po ID

```
GET /api/assets/kubernetes_namespaces/:id.json
```


## Po stanie assetu

```
GET /api/assets/kubernetes_namespaces.json?state=state

state: active, closed
```
