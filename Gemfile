source 'https://rubygems.org'

ruby '3.1.3'

# https://hashrocket.com/blog/posts/how-to-upgrade-to-rails-5
# https://edgeguides.rubyonrails.org/upgrading_ruby_on_rails.html#upgrading-from-rails-4-2-to-rails-5-0
gem 'rails', '~> 7.0', '>= 7.0.8'

gem 'aasm', '~> 5.0', '>= 5.0.6'
gem 'activerecord-cte', '~> 0.3.0'
gem 'activerecord-import', '~> 1.4'
gem 'activerecord-like', '~> 2.2'
gem 'api-pagination', '~> 5.0'
gem 'awesome_nested_set', '~> 3.2'
gem 'awesome_print'
gem 'builder', '~> 3.2'
gem 'bunny'
gem 'caxlsx', '~> 3.1'
gem 'config', '~> 3.1'
gem 'connection_pool'
gem 'cracklib_reloaded', '0.1.6', require: false
gem 'dalli', '~> 3.2'
gem 'date_validator', '~> 0.12.0'
gem 'devise-security', '~> 0.16'
gem 'devise_token_auth', '~> 1.1'
gem 'doorkeeper', '~> 5.6'
gem 'draper', '~> 4.0'
gem 'efigence-swagger_ui_wrapper', '~> 0.1.0'
gem 'exception_notification', '~> 4.1'
gem 'faraday-retry', '~> 2.0'
gem 'ffaker', '~> 2.21'
gem 'globalid', '~> 1.0'
gem 'holidays', '~> 8.6'
gem 'httparty', '~> 0.18'
gem 'httpi'
gem 'ibanizator', '~> 0.4.3'
gem 'interactor', '~> 3.1'
gem 'jbuilder', '~> 2.0'
gem 'kaminari', '~> 1.2'
gem 'mimemagic', '~> 0.3.2'
gem 'mime-types', '~> 3.3'
gem 'mysql2', '~> 0.5.3'
gem 'net-ldap', '~> 0.16'
gem 'paranoia', '~> 2.6'
gem 'pg', '~> 0.18.3'
gem 'public_activity', '~> 2.0'
gem 'puma', '~> 5.6'
gem 'punditry', '~> 0.1.2'
gem 'rack-attack', '~> 4.4'
gem 'rack-cors', '~> 1.0.6'
gem 'rails-i18n', '~> 7.0'
gem 'redis', '~> 4.5', '< 4.6.0'
gem 'redis-namespace', '~> 1.5'
gem 'request_store', '~> 1.5'
gem 'responders', '~> 3.0'
gem 'search_object', '~> 1.2'
gem 'shrine', '~> 3.4'
gem 'sidekiq', '~> 7.1'
gem 'sidekiq-failures', '~> 1.0'
gem 'sidekiq-limit_fetch', '~> 4.4'
gem 'sidekiq-scheduler', '~> 5.0'
gem 'sidekiq-status', '~> 3.0'
gem 'spreadsheet', '~> 1.2'
gem 'sshkey', '~> 1.8'
gem 'stringex', '~> 2.7'
gem 'swagger-blocks'
gem 'roo', '~> 2.7', '>= 2.7.1'

# form objects:
gem 'reform', '~> 2.2.1'
gem 'reform-rails', '~> 0.1.7'

gem 'paper_trail', '~> 12.3'

# Cache results of expensive computations
gem 'bootsnap', require: false

# records tracking with associations:
gem 'active_snapshot', '= 0.3.0.efigence'

# Fix warnings on load https://github.com/ruby/net-imap/issues/16
gem 'net-http', '~> 0.3.2'

# Enforce minitest best practices
gem 'rubocop-minitest', require: false

# GUS API client:
gem 'gus_bir1', '~> 1.2'

# Microsoft Graph Ruby SDK
gem 'microsoft_graph', '~> 0.22.1'

# opentelemetry support
gem 'opentelemetry-exporter-otlp', '~> 0.26'
gem 'opentelemetry-instrumentation-all'
gem 'opentelemetry-sdk', '~> 1.4'

# DB views:
gem 'scenic', '~> 1.8'
gem 'scenic-mysql_adapter', '~> 1.0'

group :development, :test do
  gem 'bundler-audit', require: false
  gem 'http_logger', require: false
  gem 'letter_opener_web'
  gem 'm'
  gem 'minitest', '~> 5.18.1'
  gem 'minitest-fail-fast'
  gem 'minitest-rails', '~> 7.0.0'
  gem 'minitest-rerun-options'
  gem 'minitest-vcr'
  gem 'pronto'
  gem 'pronto-rubocop', require: false
  gem 'pry-byebug'
  gem 'rubocop-rails'
  gem 'selenium-webdriver'
  gem 'webmock', '~> 3.12'
end

group :test do
  gem 'capybara-angular'
  gem 'capybara-screenshot'
  gem 'fakeredis', require: 'fakeredis/minitest'
  gem 'minitest-bang', require: false
  gem 'minitest-reporters'
  gem 'minitest-spec-context'
  gem 'mocha', '~> 1.1'
  gem 'policy-assertions', '~> 0.0.3'
  gem 'rails-controller-testing'
  gem 'shoulda', '~> 4.0'
  gem 'simplecov', require: false
  gem 'timecop'
  gem 'vcr', '~> 6.0'
end

group :development do
  # capistrano:
  gem 'airbrussh', require: false
  gem 'capistrano'
  gem 'capistrano3-puma'
  gem 'capistrano-artrails', '~> 0.1.6'
  gem 'capistrano-bundler'
  gem 'capistrano-rails'
  gem 'capistrano-rsync-bladrak', '~> 1.3', source: 'https://rubygems.org'
  gem 'capistrano-rvm'
  gem 'capistrano-scm-copy'
  gem 'capistrano-spa'

  gem 'active_record_doctor'
  gem 'brakeman', require: false
  gem 'bullet'
  gem 'bundleup', require: false
  gem 'guard'
  gem 'guard-minitest'
  gem 'rails_best_practices'
  gem 'spring'
  gem 'web-console'
end

group :production do
  gem 'turnout', '~> 2.4'
end
