# config valid only for current version of Capistrano

lock '3.17.0'

set :rvm_ruby_version, '3.1.3'
set :application,      'main'
set :repo_url,         'ssh://********************/efigence-projekt-imperator/imperator.git'
set :user,             'deploy'

set :scm,              :rsync
set :pty,              false
set :use_sudo,         false
set :stage,            :production
set :deploy_to,        "/home/<USER>/#{fetch(:application)}"

# spa
set :spa_repo_url, 'ssh://********************/efigence-projekt-imperator/imperator-front.git'
set :spa_build_cmds, [[:yarn, 'install'], [:gulp, 'clean'], [:gulp, 'build']]

# puma
set :puma_threads, [4, 16]
set :puma_bind, "unix://#{shared_path}/tmp/sockets/#{fetch(:application)}-puma.sock"
set :puma_state, "#{shared_path}/tmp/pids/puma.state"
set :puma_pid, "#{shared_path}/tmp/pids/puma.pid"
set :puma_access_log, "#{release_path}/log/puma.error.log"
set :puma_error_log,  "#{release_path}/log/puma.access.log"
set :puma_preload_app, true
set :puma_worker_timeout, nil
set :puma_init_active_record, true

# sidekiq
set :sidekiq_roles, %w[sidekiq]

# TODO: imperator-specific excludes
set :rsync_options, %w[
  --recursive --delete --delete-excluded
  --exclude .git*
  --exclude /config/sidekiq.yml
  --exclude /test/***
  --exclude /public/uploads
  --exclude /public/tmp
  --exclude /log
]

# capistrano-artrails sudo mode
set :artrails_use_sudo, true

# skip db:migrate if not changed
set :conditionally_migrate, true

# set :format,        :pretty
# set :log_level,     :debug
set :keep_releases, 5

set :linked_files, fetch(:linked_files, []).push('config/database.yml',
                                                 'config/sidekiq.yml',
                                                 'config/settings/production.yml',
                                                 'config/app.yml',
                                                 'config/secrets.yml',
                                                 'config/settings.yml')

set :linked_dirs, fetch(:linked_dirs, []).push('log',
                                               'tmp/pids',
                                               'tmp/cache',
                                               'tmp/sockets',
                                               'vendor/bundle',
                                               'public/uploads',
                                               'uploads',
                                               'files',
                                               'db/uploads')

set :bundle_without, %w[development test].join(' ')

after 'rsync:stage_done', 'spa:all'

# Override default puma commands, except puma:config
%w[start stop restart status].each do |command|
  Rake::Task["puma:#{command}"].clear
end
namespace :puma do
  %w[start stop restart status].each do |command|
    desc "#{command.capitalize} puma (artrails)"
    task command do
      invoke "artrails:app:#{command}"
    end
  end
end

namespace :imperator do
  desc 'set current global roles'
  task :set_global_roles do
    on primary(:sidekiq) do
      within current_path do
        with rails_env: fetch(:rails_env) do
          rake 'imperator:set_global_roles'
        end
      end
    end
  end
end

after 'deploy:finishing', 'imperator:set_global_roles'
after 'deploy:finishing', 'artrails:sidekiq:restart'
