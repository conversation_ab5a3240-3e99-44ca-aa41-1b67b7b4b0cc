en:
  activerecord:
    errors:
      models:
        evaluation_iteration:
          attributes:
            base:
              cannot_create_evaluation_iteration_if_some_uncompleted_exists: new iteration cannot be created if an uncompleted iteration existsx
            deadline_on:
              new_evaluation_iteration_date_cannot_be_earlier_than_the_old_ones: the existence of a future iteration prevents from (re)scheduling this one
              cannot_create_evaluation_iteration_with_deadline_in_the_past: "a new iteration's deadline cannot be in the past"
