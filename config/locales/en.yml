# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more, please read the Rails Internationalization guide
# available at http://guides.rubyonrails.org/i18n.html.

en:
  api:
    messages:
      not_found: Record not found
      not_authorized: Not authorized
      insufficient_permission_level_to_perform_this_action: Insufficient permission level to perform this action
      not_acceptable: Request not acceptable
      version_expired: API version expired
      invalid_route: Invalid API route
      bad_request: Bad request (Client Error)

  errors:
    attributes:
      download_from_gus:
        cannot_download_data: "Cannot download data from GUS."
      project:
        must_belong_to_same_company_and_client: must belong to the same company and client as the main project.
        must_have_payment_schedule: must have a payment schedule.

  activerecord:
    attributes:
      invoice/kind:
        vat: VAT
        re_invoice: Re-invoice
        advance: Advance invoice
        advance_accounting: Advance accounting invoice
        accounting_note: Accounting note
        vat_barter: Barter VAT invoice
      invoice_position/tax_rate:
        oo: o.o.
        np: np.
        zw: zw.
        "0": 0%
        "2,5": "2.5%"
        "3": 3%
        "5": 5%
        "7": 7%
        "8": 8%
        "10": 10%
        "13": 13%
        "15": 15%
        "19": 19%
        "20": 20%
        "22": 22%
        "23": 23%
        "24": 24%
        "25": 25%
      cost_invoice/payment_method:
        transfer: Bank transfer
        card: Card
        cash: Cash
        barter: Barter
        compensation: Kompensata
      cost_invoice/flow:
        project: Project flow
        general: General flow
        simplified: Simplified flow
      cost_invoice/kind:
        vat: VAT invoice
        proforma: Pro-forma invoice
        correction: Correcting/accounting invoice
        accounting_note: Accounting note
        vat_barter: Barter VAT invoice
      payment/kind:
        invoice: Invoice
      external_cost:
        mpk_number: MPK number
    model:
      invoice:
        attributes:
          title: Title
          sell_date: Sell date
          due_date: Due date
          invoice_date: Invoice date
          description: Description
          receiver_name: Receiver name
          street: Street
          street_number: House number
          apartment: Apartment number
          additional_address: Additional address
          city: City
          postcode: Postal code
          post: Post
          country: Country
          voivodeship: Voivodeship
          district: Powiat
          community: Gmina
          total_amount: Total amount
          total_amount_formatted: Total amount
          revenue_account_label: Revenue account
      user:
        attributes:
          activates_on: Activation date
    errors:
      models:
        training_request:
          budget_exceeded: budget for this year has been exceeded by %{exceeded_by}
        user_contract:
          attributes:
            agreement_type:
              unique_by_time_period: must be unique in specific time period and with specific user
        user_entry_card:
          attributes:
            user_id:
              unique_by_time_period: user cannot have more than one card simultaneously
            card_number:
              unique_by_time_period: must be unique in specific time period
        remote_work_period:
          attributes:
            place_of_work:
              blank: You must declare a place of remote work for that day/days
            user:
              not_remote: User should be marked as allowed for remote work.
              should_not_change: User should never be changed.
            starts_on:
              exceeds_limit: Exceeds yearly limit for %{year} by %{exceeded} days.
              does_not_cover_business_days: At least one business day should be covered.
              overlaps_other: This remote overlaps another one.
            ends_on:
              exceeds_limit: Exceeds yearly limit for %{year} by %{exceeded} days.
              does_not_cover_business_days: At least one business day should be covered.
              overlaps_other: This remote overlaps another one.
        b2b/cost_invoice:
          attributes:
            number:
              should_be_unique: an invoice with the given number and contractor already exists in the system
            hours_worked:
              equal_to: must be equal to hours reported in Redmine (%{count})
        dms/cost_invoice:
          attributes:
            contractor:
              should_be_active: should be active
            paid_on:
              later_than_card_expiration: payment that cannot be set to be later than card expiration date (%{date})
            number:
              should_be_unique: an invoice with the given number and contractor already exists in the system
            cost_projects:
              should_have_cost_account_numbers: allocation should have cost account numbers assigned
        cost_project:
          attributes:
            accounting_number_id:
              not_allowed: this accounting number is not allowed
        cost_invoices:
          attributes:
            document:
              format: "File type must be one of the following: pdf, doc, docx tiff jpg jpeg png bmp"
        google_service:
          at_least_one_required: at least one item required
        bio:
          attributes:
            user:
              already_has_bio: Already has bio
        accounting_number:
          attributes:
            user_id:
              changed: You can't change once set user
            projects:
              present: You can't delete accounting number with assigned projects.
        project:
          attributes:
            payment_schedule_required:
              one_per_account_number: There should only be one payment schedule per account number.
              must_be_true: Must be checked if there are any accepted/issued/pending invoices.
            personal_data:
              cannot_be_changed: can't be changed to false
            company_id:
              cannot_be_changed: can't be changed, project has assigned invoices
            bank_account_id:
              not_in_company: Bank account number should belong to the project's company.
            accounting_number_id:
              locked: This accounting number is not allowed.
            accounting_number:
              invalid_parent: Project with a non-zero account number must have a zero account number project as the parent
            cooperative_project:
              inclusion: "Must be true because the parent project is cooperative"
        payment:
          attributes:
            current_invoice:
              is_present: You can't edit payment with issued or pending invoice.
            cyclic:
              edition: You cannot modify cyclic payment which has any children.
              child_destruction: You cannot remove child cyclic payment.
              originator_destruction: You cannot remove cyclic payment which (or at least one of its children) has any invoices.
            predicted_amount:
              not_consistent_with_mpk_positions: Predicted amount is not consistent with the mpk positions.
        payment_schedule:
          attributes:
            project:
              should_require_payment_schedule: Project should require payment schedule.
        invoice:
          attributes:
            base:
              issued_cannot_be_modified: Invoice is already issued and therefore cannot be modified.
              draft_exists: There is already draft invoice for this payment.
              project_is_inactive: The project is inactive.
              equal_to_parent: Invoice does not differ from the amended invoice.
            state:
              cannot_be_switched_back_to_draft: State can't be switched back to draft.
            total_amount:
              not_consistent_with_mpk_positions: Total amount is not consistent with the mpk positions.
              not_consistent_with_invoice_positions: Total amount is not consistent with the invoice positions.
            invoice:
              issued_cannot_be_modified: You can't modify an invoice that is already issued.
            invoice_date:
              must_be_greater_or_equal_than: Invoice date must be greater or equal than %{date}
            invoice_document:
              already_exists: Invoice document already exists
        cost_invoice:
          attributes:
            base:
              accepted_cannot_be_modified: Cost Invoice is already accepted and therefore cannot be modified
            contractor:
              must_be_assigned_to_user: Contractor of the HR cost invoice has to be assigned to the User
            flow:
              not_allowed: this flow is not allowed
            total_amount:
              not_consistant_with_cost_projects: total amount is not consistent with projects costs
            overtime_selected_month:
              month_required: month is required
            overtime_selected_year:
              year_required: year is required
            overtime_invoice:
              no_data_for_overtime: no data for overtime in selected date
              overtime_changes_require_global_admin: changes to cost projects for overtime invoices require global admin or accounting role
        cost_allocation_template:
          attributes:
            total_positions_share_amount:
              is_not_equal_to_one: Positions share amounts do not sum up to 100%
        public_key:
          attributes:
            key:
              taken_by_you: 'is already in use by you as "%{name}".'
              taken_by_someone: is already in use..
              corrupted: seems to be corrupted.
            identifier:
              cannot_change: cannot be changed
        holiday_request:
          attributes:
            limit_exceeded:
              overuse_of_holidays_nz: "Warning! This request exceeds the limit of 'Niedostępność/Ż' absences for this user."
              negative_absence_balance: Warning! This request exceeds the number of available absences for this user.
              negative_sick_absence_balance: Warning! This request exceeds the number of available sick absences for this user.
        user:
          attributes:
            email:
              ascii_only: Email should only contain ASCII characters.
            activates_on:
              cannot_be_in_the_past: cannot be in the past.
              cannot_be_changed_for_active_user: cannot be changed for active user
            remote_allowed:
              only_for_coe: This option is valid only for contract of employment
        asset:
          attributes:
            user_id:
              user_or_project_required: User or Project required
              user_department_required: User must belong to department
              taken: Has already been used for another asset
            project_id:
              user_or_project_required: User or Project required
        kubernetes_namespace:
          attributes:
            repos_to_send_key:
              invalid_values: Has invalid values
  activemodel:
    attributes:
      dashboard/user_full_name:
          accepted: Accepted, to be sent
    errors:
      models:
        holiday_request_form:
          attributes:
            category:
              category_not_permittable: "Applicant cannot select `Niedostępność/Ż`, `Niedostępność/Ch` or `Niedostępność/DW` category"
        request_tracker_issue:
          attributes:
            cc:
              format: Emails in cc should have valid format
  interactors:
    close_project:
      pending_invoices: has pending invoices
      active_assets: has active assets
      non_issued_payments: has payments without issued invoice
      overhead: Accounting number is not overhead
    unarchive_project:
      ancestors_active: One of this project's parents is not active
  dms:
    cost_invoices:
      replacement_chief: replacement
