require_relative 'boot'

require 'rails'
# Pick the frameworks you want:
require 'active_model/railtie'
require 'active_job/railtie'
require 'active_record/railtie'
require 'action_controller/railtie'
require 'action_mailer/railtie'
require 'action_view/railtie'
# require 'sprockets/railtie'
require 'rails/test_unit/railtie'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

require 'swagger_ui_wrapper'
module Imperator
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    config.active_record.has_many_inversing = false

    # Save loading time after switching to zeitwerk:
    config.add_autoload_paths_to_load_path = false

    config.action_controller.default_protect_from_forgery = false

    config.eager_load_paths << Rails.root.join('test/mailers/previews').to_s

    # Use the responders controller from the responders gem
    config.app_generators.scaffold_controller :custom_responders_controller

    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.

    # Set Time.zone default to the specified zone and make Active Record auto-convert to this zone.
    # Run "rake -D time" for a list of tasks for finding time zone names. Default is UTC.
    config.time_zone = 'Warsaw'

    # The default locale is :en and all translations from config/locales/*.rb,yml are auto loaded.
    # config.i18n.load_path += Dir[Rails.root.join('my', 'locales', '*.{rb,yml}').to_s]
    # config.i18n.default_locale = :de

    config.middleware.use ::SwaggerUiWrapper::Rack::MiddleWare, {} if Settings.swagger

    config.middleware.use Rack::Attack

    config.generators do |g|
      g.template_engine       :jbuilder
      g.helper                false
      g.assets                false
      g.test_framework        :minitest, fixture: true
    end

    config.active_support.escape_html_entities_in_json = true

    config.active_job.queue_adapter = :sidekiq
    # for [ActiveJob] Enqueued ActionMailer::DeliveryJob:
    # add this to sidekiq.yml queues: 'active_job_<%= ENV['RAILS_ENV'] || 'production' %>_mailers'
    config.active_job.queue_name_prefix = "active_job_#{Rails.env}"

    config.global_id.app = 'imperator'

    config.active_record.belongs_to_required_by_default = true

    config.active_record.yaml_column_permitted_classes = [
      Symbol, Date, Time, DateTime, Hash, ActiveSupport::HashWithIndifferentAccess
    ]

    config.active_support.cache_format_version = 7.0
    config.active_support.disable_to_s_conversion = true
  end
end
