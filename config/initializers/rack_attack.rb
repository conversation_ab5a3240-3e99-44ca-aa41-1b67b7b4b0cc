class Rack::Attack
  # Throttle  IP
  throttle('req/ip', limit: Settings.throttle_req_limit, period: Settings.throttle_req_period, &:ip)

  # Throttle login attempts by IP address
  throttle('logins/ip', limit: Settings.throttle_login_limit, period: Settings.throttle_login_period) do |req|
    req.ip if req.path.to_s.match(%r{/api/auth/sign_in}) && req.post?
  end

  whitelist('allow to /api/check_absences/check') do |req|
    req.path.to_s.match(%r{/api/check_absences/check}) && req.post?
  end

  # Response for throttled clients
  self.throttled_response = ->(env) {
    retry_after = (env['rack.attack.match_data'] || {})[:period]
    [
      429,
      { 'Content-Type' => 'application/json', 'Retry-After' => retry_after.to_s },
      [{ error: 'Request limit reached. Retry later.' }.to_json]
    ]
  }

  ActiveSupport::Notifications.subscribe('rack.attack') do |_name, _start, _finish, _request_id, req|
    if req.env['rack.attack.match_type'] == :throttle
      Rails.logger.info "[API REQUEST LIMIT REACHED FOR IP##{req.env['action_dispatch.remote_ip']}, at #{Time.now}]"
    end
  end
end
