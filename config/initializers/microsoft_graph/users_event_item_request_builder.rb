# handle `send_async': factory cannot be null (StandardError)
# i.e. 204 No Content response in delete endpoint

MicrosoftGraph::Users::Item::Events::Item::EventItemRequestBuilder.class_eval do
  # override method defined in microsoft_graph-0.22.1/lib/users/item/events/item/event_item_request_builder.rb:99
  def delete(request_configuration = nil)
    request_info = to_delete_request_information(request_configuration)
    error_mapping = {}
    error_mapping['4XX'] = ->(pn) { MicrosoftGraph::Models::ODataErrorsODataError.create_from_discriminator_value(pn) }
    error_mapping['5XX'] = ->(pn) { MicrosoftGraph::Models::ODataErrorsODataError.create_from_discriminator_value(pn) }

    @request_adapter.send_async(request_info, true, error_mapping)
  end
end
