# enables or disables the escaping of HTML entities in JSON serialization. Defaults to false.
# Rails.application.config.active_support.escape_html_entities_in_json = false

# escapes angular expression {{}} => \{\{\}\}
ANGULAR_ESCAPE = { '{' => '\{', '}' => '\}' }.freeze
ANGULAR_ESCAPE_REGEXP = /[}{]/u

# silence_warnings do
# end

# enables or disables serializing dates to ISO 8601 format. Defaults to true.
# Rails.application.config.active_support.use_standard_json_time_format = true

# sets the precision of JSON encoded time values. Defaults to 3.
# Rails.application.config.active_support.time_precision = 3
