# Be sure to restart your server when you modify this file.

# Configure parameters to be filtered from the log file. Use this to limit dissemination of
# sensitive information. See the ActiveSupport::ParameterFilter documentation for supported
# notations and behaviors.
Rails.application.config.filter_parameters += [
  :passw, :secret, :token, :_key, :crypt, :salt, :certificate, :otp, :ssn, 'access-token', 'base64'
]

Rails.application.config.filter_parameters << lambda do |_key, value|
  if value.is_a?(Hash)
    filter_with = Rails.application.config.filter_parameters
    ActiveSupport::ParameterFilter.new(filter_with).filter(value)
  end
end
