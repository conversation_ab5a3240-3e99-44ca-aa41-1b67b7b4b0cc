APPLICATION_HOST = Settings.mailer.default_url_options['host']
APPLICATION_PROTOCOL = Settings.mailer.default_url_options['protocol']
APPLICATION_SCHEME = APPLICATION_PROTOCOL + '://'
APPLICATION_URL = APPLICATION_SCHEME + APPLICATION_HOST

Imperator::Application.config.action_mailer.default_url_options = {
  host: APPLICATION_HOST,
  protocol: APPLICATION_PROTOCOL
}

ActionMailer::Base.default(
  from: Settings.mailer['from'],
  reply_to: Settings.mailer['reply_to'],
  'X-Mail-Class' => proc { (defined?(action_name) && action_name) || 'undefined' }
)

ActionMailer::Base.smtp_settings = Settings.mailer.smtp_settings.to_h
Rails.application.config.action_mailer.smtp_settings = ActionMailer::Base.smtp_settings
Imperator::Application.default_url_options = Imperator::Application.config.action_mailer.default_url_options

if Rails.env.production?
  require 'mailer_interceptor'
  ActionMailer::Base.register_interceptor(MailerInterceptor)
end
# fix for sidekiq: uninitialized constant Mail::Parsers::ContentTransferEncodingParser
require 'mail'
Mail.eager_autoload!
