# Monkey patch for HTTParty
# Add PROPFIND method
module HTTParty
  class Request
    SupportedHTTPMethods << Net::HTTP::Propfind
  end

  module ClassMethods
    # Perform a PROPFIND request to a path
    def propfind(path, options = {}, &block)
      perform_request Net::HTTP::Propfind, path, options, &block
    end
  end

  class Basement
    include HTTParty
  end

  def self.propfind(*args, &block)
    Basement.propfind(*args, &block)
  end
end
