# Connection pool in Rails config/database.yml must have a larger value than the Concurrency level set in config/sidekiq.yml
# https://blog.stromich.com/2015/08/19/rails-database-connection-pool-and-sidekiq-concurrency/
# Sidekiq uses threads to handle many jobs at the same time in the same process.
# To prevent sharing of connections you need to configure the Sidekiq server to correctly establish it's own connection.

require 'sidekiq-status'

Sidekiq.default_job_options = Sidekiq.default_job_options.merge('queue' => 'imperator')

redis_options = Settings.redis.to_h

Sidekiq.configure_client do |config|
  config.logger.level = Rails.logger.level
  # default client pool size is 5, should not be higher than pool size, but does not have to be as high as server size (save unused Redis connections)
  config.redis = redis_options
  Sidekiq::Status.configure_client_middleware config
end

Sidekiq.configure_server do |config|
  config.logger.level = Rails.logger.level
  # default server size is 25
  config.redis = redis_options
  # config = ActiveRecord::Base.configurations[Rails.env] || Rails.application.config.database_configuration[Rails.env]
  # ActiveRecord::Base.establish_connection(config)
  # accepts :expiration (optional)
  Sidekiq::Status.configure_server_middleware config

  # accepts :expiration (optional)
  Sidekiq::Status.configure_client_middleware config
end

# setting this in yml does not work as advertised
# TODO: try using config.options[:queues] << 'default' inside config blocks
Sidekiq::Queue["active_job_#{ENV['RAILS_ENV'] || 'production'}_mailers"].limit = 5
Sidekiq::Queue["active_job_#{ENV['RAILS_ENV'] || 'production'}_default"].limit = 5
Sidekiq::Queue["active_job_#{ENV['RAILS_ENV'] || 'production'}_audit"].limit = 5
if Rails.env.development?
  Sidekiq::Queue['ldap'].limit = 1
  Sidekiq::Queue['ldap'].process_limit = 1
  Sidekiq::Queue['redmine'].limit = 1
  Sidekiq::Queue['redmine'].process_limit = 1
  Sidekiq::Queue['owncloud'].limit = 1
  Sidekiq::Queue['owncloud'].process_limit = 1
  Sidekiq::Queue['audit'].limit = 1
  Sidekiq::Queue['audit'].process_limit = 1
end
