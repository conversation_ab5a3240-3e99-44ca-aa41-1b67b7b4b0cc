GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.2.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_record_doctor (1.9.0)
      activerecord (>= 4.2.0)
    active_snapshot (0.3.0.efigence)
      activerecord
      activerecord-import
      railties
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.2)
      activemodel (> 5.x)
      activesupport (> 5.x)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activerecord-cte (0.3.0)
      activerecord
    activerecord-import (1.4.1)
      activerecord (>= 4.2)
    activerecord-like (2.2)
      activerecord (>= 5.0.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    adamantium (0.2.0)
      ice_nine (~> 0.11.0)
      memoizable (~> 0.4.0)
    addressable (2.8.5)
      public_suffix (>= 2.0.2, < 6.0)
    airbrussh (1.4.0)
      sshkit (>= 1.6.1, != 1.7.0)
    akami (1.3.1)
      gyoku (>= 0.4.0)
      nokogiri
    amq-protocol (2.3.2)
    ansi (1.5.0)
    api-pagination (5.0.0)
    ast (2.4.2)
    awesome_nested_set (3.5.0)
      activerecord (>= 4.0.0, < 7.1)
    awesome_print (1.9.2)
    bcrypt (3.1.18)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    bootsnap (1.13.0)
      msgpack (~> 1.2)
    brakeman (5.2.3)
    builder (3.3.0)
    bullet (7.0.2)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundler-audit (0.9.1)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    bundleup (2.2.3)
    bunny (2.20.3)
      amq-protocol (~> 2.3, >= 2.3.1)
      sorted_set (~> 1, >= 1.0.2)
    byebug (11.1.3)
    capistrano (3.17.0)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-artrails (0.1.8)
      capistrano (>= 3.0)
    capistrano-bundler (1.6.0)
      capistrano (~> 3.1)
    capistrano-rails (1.6.2)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rsync-bladrak (1.4.2)
      capistrano (>= 3.0.0.pre14, < 4)
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capistrano-scm-copy (0.7.0)
      capistrano (~> 3.0)
    capistrano-spa (0.0.1)
      capistrano (~> 3.1)
      capistrano-bundler (~> 1.1)
    capistrano3-puma (5.2.0)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (>= 4.0, < 6.0)
    capybara (3.36.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-angular (0.2.6)
      capybara (>= 2.5.0)
    capybara-screenshot (1.0.26)
      capybara (>= 1.0, < 4)
      launchy
    caxlsx (3.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    childprocess (4.1.0)
    chronic_duration (0.10.6)
      numerizer (~> 0.1.1)
    code_analyzer (0.5.5)
      sexp_processor
    coderay (1.1.3)
    concurrent-ruby (1.3.4)
    config (3.1.1)
      deep_merge (~> 1.2, >= 1.2.1)
      dry-validation (~> 1.0, >= 1.0.0)
    connection_pool (2.4.1)
    content_disposition (1.0.0)
    crack (0.4.5)
      rexml
    cracklib_reloaded (0.1.6)
      ffi (~> 1, >= 1.0.0)
    crass (1.0.6)
    dalli (3.2.3)
    date (3.4.1)
    date_validator (0.12.0)
      activemodel (>= 3)
      activesupport (>= 3)
    declarative (0.0.20)
    declarative-builder (0.1.0)
      declarative-option (< 0.2.0)
    declarative-option (0.1.0)
    deep_merge (1.2.2)
    devise (4.8.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-security (0.17.0)
      devise (>= 4.3.0)
    devise_token_auth (1.2.2)
      bcrypt (~> 3.0)
      devise (> 3.5.2, < 5)
      rails (>= 4.2.0, < 7.1)
    disposable (0.4.7)
      declarative (>= 0.0.9, < 1.0.0)
      declarative-builder (< 0.2.0)
      declarative-option (< 0.2.0)
      representable (>= 2.4.0, <= 3.1.0)
      uber (< 0.2.0)
    docile (1.4.0)
    doorkeeper (5.6.6)
      railties (>= 5)
    down (5.4.1)
      addressable (~> 2.8)
    draper (4.0.2)
      actionpack (>= 5.0)
      activemodel (>= 5.0)
      activemodel-serializers-xml (>= 1.0)
      activesupport (>= 5.0)
      request_store (>= 1.0)
      ruby2_keywords
    dry-configurable (0.13.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 0.6)
    dry-container (0.9.0)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 0.13, >= 0.13.0)
    dry-core (0.7.1)
      concurrent-ruby (~> 1.0)
    dry-inflector (0.2.1)
    dry-initializer (3.0.4)
    dry-logic (1.2.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 0.5, >= 0.5)
    dry-schema (1.8.0)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 0.13, >= 0.13.0)
      dry-core (~> 0.5, >= 0.5)
      dry-initializer (~> 3.0)
      dry-logic (~> 1.0)
      dry-types (~> 1.5)
    dry-types (1.5.1)
      concurrent-ruby (~> 1.0)
      dry-container (~> 0.3)
      dry-core (~> 0.5, >= 0.5)
      dry-inflector (~> 0.1, >= 0.1.2)
      dry-logic (~> 1.0, >= 1.0.2)
    dry-validation (1.7.0)
      concurrent-ruby (~> 1.0)
      dry-container (~> 0.7, >= 0.7.1)
      dry-core (~> 0.5, >= 0.5)
      dry-initializer (~> 3.0)
      dry-schema (~> 1.8, >= 1.8.0)
    efigence-swagger_ui_wrapper (0.1.1)
      rack (>= 1.3.0)
    equalizer (0.0.11)
    erubi (1.13.1)
    erubis (2.7.0)
    et-orbi (1.2.11)
      tzinfo
    exception_notification (4.5.0)
      actionmailer (>= 5.2, < 8)
      activesupport (>= 5.2, < 8)
    fakeredis (0.7.0)
      redis (>= 3.2, < 5.0)
    faraday (2.7.4)
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-net_http (3.0.2)
    faraday-retry (2.0.0)
      faraday (~> 2.0)
    ffaker (2.21.0)
    ffi (1.15.5)
    formatador (1.1.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gitlab (4.19.0)
      httparty (~> 0.20)
      terminal-table (>= 1.5.1)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-protobuf (4.28.2-x86_64-linux)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos-types (1.16.0)
      google-protobuf (>= 3.18, < 5.a)
    guard (2.18.0)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-minitest (2.4.6)
      guard-compat (~> 1.2)
      minitest (>= 3.0)
    gus_bir1 (1.2.1)
      savon (~> 2.14)
      savon-multipart
    gyoku (1.4.0)
      builder (>= 2.1.2)
      rexml (~> 3.0)
    hashdiff (1.0.1)
    hashie (5.0.0)
    holidays (8.6.0)
    htmlentities (4.3.4)
    http_logger (0.7.0)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpi (3.0.1)
      rack
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    ibanizator (0.4.12)
      adamantium (~> 0.2.0)
      equalizer (~> 0.0.11)
    ice_nine (0.11.2)
    interactor (3.1.2)
    iso8601 (0.13.0)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.6.3)
    jwt (2.7.1)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    launchy (2.5.0)
      addressable (~> 2.7)
    letter_opener (1.8.1)
      launchy (>= 2.2, < 3)
    letter_opener_web (1.4.1)
      actionmailer (>= 3.2)
      letter_opener (~> 1.0)
      railties (>= 3.2)
    listen (3.7.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lumberjack (1.2.8)
    m (1.6.0)
      method_source (>= 0.6.7)
      rake (>= *******)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memoizable (0.4.2)
      thread_safe (~> 0.3, >= 0.3.1)
    method_source (1.1.0)
    microsoft_graph (0.22.1)
      microsoft_graph_core (~> 0.3.1)
    microsoft_graph_core (0.3.1)
      microsoft_kiota_abstractions (~> 0.14.0)
      microsoft_kiota_authentication_oauth (~> 0.8.0)
      microsoft_kiota_faraday (~> 0.12.0)
      microsoft_kiota_serialization_json (~> 0.9.0)
    microsoft_kiota_abstractions (0.14.3)
      iso8601 (~> 0.13.0)
      stduritemplate (~> 0.0.39)
    microsoft_kiota_authentication_oauth (0.8.0)
      microsoft_kiota_abstractions (~> 0.14.0)
      oauth2 (~> 2.0)
    microsoft_kiota_faraday (0.12.0)
      faraday (~> 2.7, >= 2.7.2)
      microsoft_kiota_abstractions (~> 0.14.0)
    microsoft_kiota_serialization_json (0.9.1)
      json (~> 2.6.3)
      microsoft_kiota_abstractions (~> 0.14.0)
      uuidtools (~> 2.2.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_mime (1.1.5)
    minispec-metadata (2.0.0)
      minitest
    minitest (5.18.1)
    minitest-bang (1.0.4)
      minitest (>= 4.7.5)
    minitest-fail-fast (0.1.0)
      minitest (~> 5)
    minitest-rails (7.0.1)
      minitest (~> 5.10)
      railties (~> 7.0.0)
    minitest-reporters (1.4.3)
      ansi
      builder
      minitest (>= 5.0)
      ruby-progressbar
    minitest-rerun-options (0.0.2)
    minitest-spec-context (0.0.4)
    minitest-vcr (1.4.0)
      minispec-metadata (~> 2.0)
      minitest (>= 4.7.5)
      vcr (>= 2.9)
    mocha (1.14.0)
    msgpack (1.5.4)
    multi_xml (0.6.0)
    mysql2 (0.5.4)
    nenv (0.3.0)
    net-http (0.3.2)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-ldap (0.17.1)
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (1.2.1)
      net-ssh (>= 2.6.5)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.0.1)
    nio4r (2.7.3)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nori (2.6.0)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    numerizer (0.1.1)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    octokit (6.0.1)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    opentelemetry-api (1.2.5)
    opentelemetry-common (0.21.0)
      opentelemetry-api (~> 1.0)
    opentelemetry-exporter-otlp (0.29.0)
      google-protobuf (>= 3.18)
      googleapis-common-protos-types (~> 1.3)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-sdk (~> 1.2)
      opentelemetry-semantic_conventions
    opentelemetry-helpers-mysql (0.1.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.20)
    opentelemetry-helpers-sql-obfuscation (0.1.0)
      opentelemetry-common (~> 0.20)
    opentelemetry-instrumentation-action_pack (0.9.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-action_view (0.7.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.1)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-active_job (0.7.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-active_model_serializers (0.20.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-active_record (0.7.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-active_support (0.5.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-all (0.60.0)
      opentelemetry-instrumentation-active_model_serializers (~> 0.20.1)
      opentelemetry-instrumentation-aws_sdk (~> 0.5.0)
      opentelemetry-instrumentation-bunny (~> 0.21.0)
      opentelemetry-instrumentation-concurrent_ruby (~> 0.21.1)
      opentelemetry-instrumentation-dalli (~> 0.25.0)
      opentelemetry-instrumentation-delayed_job (~> 0.22.0)
      opentelemetry-instrumentation-ethon (~> 0.21.1)
      opentelemetry-instrumentation-excon (~> 0.22.0)
      opentelemetry-instrumentation-faraday (~> 0.24.0)
      opentelemetry-instrumentation-grape (~> 0.1.3)
      opentelemetry-instrumentation-graphql (~> 0.28.0)
      opentelemetry-instrumentation-gruf (~> 0.2.0)
      opentelemetry-instrumentation-http (~> 0.23.1)
      opentelemetry-instrumentation-http_client (~> 0.22.1)
      opentelemetry-instrumentation-koala (~> 0.20.1)
      opentelemetry-instrumentation-lmdb (~> 0.22.1)
      opentelemetry-instrumentation-mongo (~> 0.22.1)
      opentelemetry-instrumentation-mysql2 (~> 0.27.0)
      opentelemetry-instrumentation-net_http (~> 0.22.1)
      opentelemetry-instrumentation-pg (~> 0.27.0)
      opentelemetry-instrumentation-que (~> 0.8.0)
      opentelemetry-instrumentation-racecar (~> 0.3.0)
      opentelemetry-instrumentation-rack (~> 0.24.0)
      opentelemetry-instrumentation-rails (~> 0.30.0)
      opentelemetry-instrumentation-rake (~> 0.2.1)
      opentelemetry-instrumentation-rdkafka (~> 0.4.0)
      opentelemetry-instrumentation-redis (~> 0.25.1)
      opentelemetry-instrumentation-resque (~> 0.5.0)
      opentelemetry-instrumentation-restclient (~> 0.22.1)
      opentelemetry-instrumentation-ruby_kafka (~> 0.21.0)
      opentelemetry-instrumentation-sidekiq (~> 0.25.0)
      opentelemetry-instrumentation-sinatra (~> 0.23.1)
      opentelemetry-instrumentation-trilogy (~> 0.59.0)
    opentelemetry-instrumentation-aws_sdk (0.5.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-base (0.22.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-registry (~> 0.1)
    opentelemetry-instrumentation-bunny (0.21.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-concurrent_ruby (0.21.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-dalli (0.25.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-delayed_job (0.22.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-ethon (0.21.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-excon (0.22.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-faraday (0.24.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-grape (0.1.8)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-graphql (0.28.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-gruf (0.2.1)
      opentelemetry-api (>= 1.0.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-http (0.23.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-http_client (0.22.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-koala (0.20.4)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-lmdb (0.22.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-mongo (0.22.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-mysql2 (0.27.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-mysql
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-net_http (0.22.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-pg (0.27.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-que (0.8.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-racecar (0.3.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-rack (0.24.4)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-rails (0.30.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-action_pack (~> 0.9.0)
      opentelemetry-instrumentation-action_view (~> 0.7.0)
      opentelemetry-instrumentation-active_job (~> 0.7.0)
      opentelemetry-instrumentation-active_record (~> 0.7.0)
      opentelemetry-instrumentation-active_support (~> 0.5.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-rake (0.2.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-rdkafka (0.4.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-redis (0.25.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-resque (0.5.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-restclient (0.22.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-ruby_kafka (0.21.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-sidekiq (0.25.4)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-sinatra (0.23.4)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-trilogy (0.59.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-mysql
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.22.1)
      opentelemetry-semantic_conventions (>= 1.8.0)
    opentelemetry-registry (0.3.1)
      opentelemetry-api (~> 1.1)
    opentelemetry-sdk (1.4.1)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-registry (~> 0.2)
      opentelemetry-semantic_conventions
    opentelemetry-semantic_conventions (1.10.0)
      opentelemetry-api (~> 1.0)
    orm_adapter (0.5.0)
    paper_trail (12.3.0)
      activerecord (>= 5.2)
      request_store (~> 1.1)
    parallel (1.22.1)
    paranoia (2.6.1)
      activerecord (>= 5.1, < 7.1)
    parser (*******)
      ast (~> 2.4.1)
    pg (0.18.4)
    policy-assertions (0.0.3)
      activesupport (>= 3.0.0)
      pundit (>= 1.0.0)
    pronto (0.11.1)
      gitlab (>= 4.4.0, < 5.0)
      httparty (>= 0.13.7, < 1.0)
      octokit (>= 4.7.0, < 7.0)
      rainbow (>= 2.2, < 4.0)
      rexml (>= 3.2.5, < 4.0)
      rugged (>= 0.23.0, < 2.0)
      thor (>= 0.20.3, < 2.0)
    pronto-rubocop (0.11.5)
      pronto (~> 0.11.0)
      rubocop (>= 0.63.1, < 2.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    public_activity (2.0.2)
      actionpack (>= 5.0.0)
      activerecord (>= 5.0)
      i18n (>= 0.5.0)
      railties (>= 5.0.0)
    public_suffix (5.0.3)
    puma (5.6.9)
      nio4r (~> 2.0)
    pundit (2.2.0)
      activesupport (>= 3.0.0)
    punditry (0.1.2)
      pundit
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.16)
    rack-accept (0.4.5)
      rack (>= 0.4)
    rack-attack (4.4.1)
      rack
    rack-cors (1.0.6)
      rack (>= 1.6.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.8)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    rails_best_practices (1.23.1)
      activesupport
      code_analyzer (~> 0.5.5)
      erubis
      i18n
      json
      require_all (~> 3.0)
      ruby-progressbar
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.1)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rbtree (0.4.6)
    redis (4.5.1)
    redis-client (0.17.0)
      connection_pool
    redis-namespace (1.8.2)
      redis (>= 3.0.4)
    reform (2.2.4)
      disposable (>= 0.4.1)
      representable (>= 2.4.0, < 3.1.0)
    reform-rails (0.1.7)
      activemodel (>= 3.2)
      reform (>= 2.2.0)
    regexp_parser (2.7.0)
    representable (3.0.4)
      declarative (< 0.1.0)
      declarative-option (< 0.2.0)
      uber (< 0.2.0)
    request_store (1.5.1)
      rack (>= 1.4)
    require_all (3.0.0)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    rexml (3.3.9)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rubocop (1.48.0)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.26.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.27.0)
      parser (>= *******)
    rubocop-minitest (0.26.1)
      rubocop (>= 0.90, < 2.0)
    rubocop-rails (2.9.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 0.90.0, < 2.0)
    ruby-ole (********)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    rufus-scheduler (3.8.1)
      fugit (~> 1.1, >= 1.1.6)
    rugged (1.5.1)
    savon (2.14.0)
      akami (~> 1.2)
      builder (>= 2.1.2)
      gyoku (~> 1.2)
      httpi (>= 2.4.5)
      mail (~> 2.5)
      nokogiri (>= 1.8.1)
      nori (~> 2.4)
      wasabi (~> 3.4)
    savon-multipart (2.1.2)
      mail (~> 2)
      savon (~> 2)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scenic-mysql_adapter (1.0.1)
      mysql2
      scenic (>= 1.4.0)
    search_object (1.2.5)
    selenium-webdriver (4.1.0)
      childprocess (>= 0.5, < 5.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2)
    set (1.0.3)
    sexp_processor (4.16.1)
    shellany (0.0.1)
    shoulda (4.0.0)
      shoulda-context (~> 2.0)
      shoulda-matchers (~> 4.0)
    shoulda-context (2.0.0)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    shrine (3.5.0)
      content_disposition (~> 1.0)
      down (~> 5.1)
    sidekiq (7.1.4)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.14.0)
    sidekiq-failures (1.0.1)
      sidekiq (>= 4.0.0)
    sidekiq-limit_fetch (4.4.1)
      sidekiq (>= 6)
    sidekiq-scheduler (5.0.3)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0)
    sidekiq-status (3.0.3)
      chronic_duration
      sidekiq (>= 6.0, < 8)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sorted_set (1.0.3)
      rbtree
      set (~> 1.0)
    spreadsheet (1.3.0)
      ruby-ole
    spring (4.1.1)
    sshkey (1.9.0)
    sshkit (1.21.2)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    stduritemplate (0.0.47)
    stringex (2.8.5)
    swagger-blocks (3.0.0)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    thor (1.3.2)
    thread_safe (0.3.6)
    tilt (2.0.10)
    timecop (0.9.5)
    timeout (0.4.3)
    turnout (2.5.0)
      i18n (>= 0.7, < 2)
      rack (>= 1.3, < 3)
      rack-accept (~> 0.4)
      tilt (>= 1.4, < 3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (2.4.2)
    uniform_notifier (1.16.0)
    uri (1.0.3)
    uuidtools (2.2.0)
    vcr (6.1.0)
    version_gem (1.1.3)
    warden (1.2.9)
      rack (>= 2.0.9)
    wasabi (3.8.0)
      addressable
      httpi (~> 3.0)
      nokogiri (>= 1.4.2)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.14.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.18)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  aasm (~> 5.0, >= 5.0.6)
  active_record_doctor
  active_snapshot (= 0.3.0.efigence)
  activerecord-cte (~> 0.3.0)
  activerecord-import (~> 1.4)
  activerecord-like (~> 2.2)
  airbrussh
  api-pagination (~> 5.0)
  awesome_nested_set (~> 3.2)
  awesome_print
  bootsnap
  brakeman
  builder (~> 3.2)
  bullet
  bundler-audit
  bundleup
  bunny
  capistrano
  capistrano-artrails (~> 0.1.6)
  capistrano-bundler
  capistrano-rails
  capistrano-rsync-bladrak (~> 1.3)!
  capistrano-rvm
  capistrano-scm-copy
  capistrano-spa
  capistrano3-puma
  capybara-angular
  capybara-screenshot
  caxlsx (~> 3.1)
  config (~> 3.1)
  connection_pool
  cracklib_reloaded (= 0.1.6)
  dalli (~> 3.2)
  date_validator (~> 0.12.0)
  devise-security (~> 0.16)
  devise_token_auth (~> 1.1)
  doorkeeper (~> 5.6)
  draper (~> 4.0)
  efigence-swagger_ui_wrapper (~> 0.1.0)
  exception_notification (~> 4.1)
  fakeredis
  faraday-retry (~> 2.0)
  ffaker (~> 2.21)
  globalid (~> 1.0)
  guard
  guard-minitest
  gus_bir1 (~> 1.2)
  holidays (~> 8.6)
  http_logger
  httparty (~> 0.18)
  httpi
  ibanizator (~> 0.4.3)
  interactor (~> 3.1)
  jbuilder (~> 2.0)
  kaminari (~> 1.2)
  letter_opener_web
  m
  microsoft_graph (~> 0.22.1)
  mime-types (~> 3.3)
  mimemagic (~> 0.3.2)
  minitest (~> 5.18.1)
  minitest-bang
  minitest-fail-fast
  minitest-rails (~> 7.0.0)
  minitest-reporters
  minitest-rerun-options
  minitest-spec-context
  minitest-vcr
  mocha (~> 1.1)
  mysql2 (~> 0.5.3)
  net-http (~> 0.3.2)
  net-ldap (~> 0.16)
  opentelemetry-exporter-otlp (~> 0.26)
  opentelemetry-instrumentation-all
  opentelemetry-sdk (~> 1.4)
  paper_trail (~> 12.3)
  paranoia (~> 2.6)
  pg (~> 0.18.3)
  policy-assertions (~> 0.0.3)
  pronto
  pronto-rubocop
  pry-byebug
  public_activity (~> 2.0)
  puma (~> 5.6)
  punditry (~> 0.1.2)
  rack-attack (~> 4.4)
  rack-cors (~> 1.0.6)
  rails (~> 7.0, >= 7.0.8)
  rails-controller-testing
  rails-i18n (~> 7.0)
  rails_best_practices
  redis (~> 4.5, < 4.6.0)
  redis-namespace (~> 1.5)
  reform (~> 2.2.1)
  reform-rails (~> 0.1.7)
  request_store (~> 1.5)
  responders (~> 3.0)
  roo (~> 2.7, >= 2.7.1)
  rubocop-minitest
  rubocop-rails
  scenic (~> 1.8)
  scenic-mysql_adapter (~> 1.0)
  search_object (~> 1.2)
  selenium-webdriver
  shoulda (~> 4.0)
  shrine (~> 3.4)
  sidekiq (~> 7.1)
  sidekiq-failures (~> 1.0)
  sidekiq-limit_fetch (~> 4.4)
  sidekiq-scheduler (~> 5.0)
  sidekiq-status (~> 3.0)
  simplecov
  spreadsheet (~> 1.2)
  spring
  sshkey (~> 1.8)
  stringex (~> 2.7)
  swagger-blocks
  timecop
  turnout (~> 2.4)
  vcr (~> 6.0)
  web-console
  webmock (~> 3.12)

RUBY VERSION
   ruby 3.1.3p185

BUNDLED WITH
   2.5.17
